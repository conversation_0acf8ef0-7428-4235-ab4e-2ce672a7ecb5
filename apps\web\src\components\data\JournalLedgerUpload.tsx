"use client";

import { useState, useEffect } from "react";
import { FileUpload } from "./FileUpload";
import { ColumnMapper } from "./ColumnMapper";
import { DataPreview } from "./DataPreview";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Loader2, AlertCircle, CheckCircle2 } from "lucide-react";
import { parseFile, getColumnHeaders, mapData } from "@/lib/utils/file-parser";
import { ColumnMapping, JournalLedgerEntry } from "@/types";

interface JournalLedgerUploadProps {
  engagementId: string;
  onUploadComplete?: () => void;
}

export function JournalLedgerUpload({
  engagementId,
  onUploadComplete,
}: JournalLedgerUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<Record<string, any>[]>([]);
  const [sourceColumns, setSourceColumns] = useState<string[]>([]);
  const [mappings, setMappings] = useState<ColumnMapping[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { toast } = useToast();

  // Define target columns for journal ledger
  const targetColumns = [
    { name: "transaction_date", required: true },
    { name: "account_code", required: true },
    { name: "account_name", required: true },
    { name: "amount", required: true },
    { name: "description", required: false },
    { name: "name", required: false },
    { name: "class", required: false },
    { name: "transaction_number", required: false },
    { name: "transaction_type", required: false },
    { name: "split_account", required: false },
  ];

  // Parse file when selected
  useEffect(() => {
    const parseSelectedFile = async () => {
      if (!file) {
        setParsedData([]);
        setSourceColumns([]);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        setSuccess(false);

        const data = await parseFile(file);
        setParsedData(data);

        const headers = getColumnHeaders(data);
        setSourceColumns(headers);
      } catch (err) {
        setError(`Failed to parse file: ${(err as Error).message}`);
        toast({
          title: "Error parsing file",
          description: (err as Error).message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    parseSelectedFile();
  }, [file, toast]);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setSuccess(false);
    setError(null);
  };

  const handleMappingsChange = (newMappings: ColumnMapping[]) => {
    setMappings(newMappings);
  };

  const handleUpload = async () => {
    if (!file || !parsedData.length || !mappings.length) {
      setError("Please select a file and map the columns");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setSuccess(false);

      // Map the data using the column mappings
      const mappedData = mapData<JournalLedgerEntry>(parsedData, mappings);

      // Upload the data
      const response = await fetch("/api/data/journal-ledger", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          entries: mappedData,
          engagementId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload journal ledger");
      }

      setSuccess(true);
      toast({
        title: "Upload successful",
        description: `Successfully uploaded ${mappedData.length} journal ledger entries`,
      });

      // Reset the form
      setFile(null);
      setParsedData([]);
      setSourceColumns([]);
      setMappings([]);

      // Notify parent component
      if (onUploadComplete) {
        onUploadComplete();
      }
    } catch (err) {
      setError(`Upload failed: ${(err as Error).message}`);
      toast({
        title: "Upload failed",
        description: (err as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Upload Journal Ledger</CardTitle>
        </CardHeader>
        <CardContent>
          <FileUpload
            onFileSelect={handleFileSelect}
            acceptedFileTypes=".csv,.xlsx,.xls"
            maxSizeInMB={10}
          />
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Processing...</span>
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            Journal ledger data uploaded successfully
          </AlertDescription>
        </Alert>
      )}

      {sourceColumns.length > 0 && !isLoading && !success && (
        <div className="space-y-6">
          <ColumnMapper
            sourceColumns={sourceColumns}
            targetColumns={targetColumns}
            onChange={handleMappingsChange}
          />

          {mappings.length > 0 && (
            <>
              <DataPreview
                data={parsedData}
                mappings={mappings}
                maxRows={5}
              />

              <div className="flex justify-end">
                <Button
                  onClick={handleUpload}
                  disabled={isLoading || mappings.length === 0}
                >
                  {isLoading && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Upload Journal Ledger
                </Button>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
