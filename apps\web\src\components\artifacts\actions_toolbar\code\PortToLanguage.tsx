/**
 * This component was used for code-related features that have been removed from the application.
 * It is kept for reference but is no longer used.
 */

import { ProgrammingLanguageOptions } from "@opencanvas/shared/types";
import { useToast } from "@/hooks/use-toast";
import { ProgrammingLanguageList } from "@/components/ui/programming-lang-dropdown";
import { GraphInput } from "@opencanvas/shared/types";

export interface PortToLanguageOptionsProps {
  streamMessage: (params: GraphInput) => Promise<void>;
  handleClose: () => void;
  language: ProgrammingLanguageOptions;
}

const prettifyLanguage = (language: ProgrammingLanguageOptions) => {
  switch (language) {
    case "php":
      return "PHP";
    case "typescript":
      return "TypeScript";
    case "javascript":
      return "JavaScript";
    case "cpp":
      return "C++";
    case "java":
      return "Java";
    case "python":
      return "Python";
    case "html":
      return "HTML";
    case "sql":
      return "SQL";
    default:
      return language;
  }
};

export function PortToLanguageOptions(props: PortToLanguageOptionsProps) {
  const { streamMessage } = props;
  const { toast } = useToast();

  // This component is no longer used as code-related features have been removed
  const handleSubmit = async (portLanguage: ProgrammingLanguageOptions) => {
    toast({
      title: "Feature removed",
      description: "Code-related features have been removed from the application",
      duration: 5000,
    });
    props.handleClose();
  };

  return <ProgrammingLanguageList handleSubmit={handleSubmit} />;
}
