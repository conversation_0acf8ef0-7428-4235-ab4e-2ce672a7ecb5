import { ProgrammingLanguageOptions } from "@opencanvas/shared/types";

export interface TrialBalanceEntry {
  id?: string;
  engagement_id?: string;
  balance_date: string;
  account_code: string;
  account_name: string;
  amount: number;
  account_type?: string;
  financial_statement_category?: string;
  audit_testing_section?: string;
  created_by?: string;
  created_at?: string;
  updated_at?: string;
}

export interface JournalLedgerEntry {
  id?: string;
  engagement_id?: string;
  transaction_date: string;
  description?: string;
  account_name: string;
  name?: string;
  amount: number;
  account_code: string;
  class?: string;
  transaction_number?: string;
  transaction_type?: string;
  split_account?: string;
  created_by?: string;
  created_at?: string;
}

export interface FileUploadResult {
  success: boolean;
  data?: any;
  error?: string;
}

export interface ColumnMapping {
  sourceColumn: string;
  targetColumn: string;
  required: boolean;
}

export type Message = {
  id: string;
  text?: string;
  rawResponse?: Record<string, any>;
  sender: string;
  toolCalls?: ToolCall[];
};

export interface ToolCall {
  id: string;
  name: string;
  args: string;
  result?: any;
}

export type Model = "gpt-4o-mini" | string; // Add other model options as needed

export type UserRules = {
  styleRules: string[];
  contentRules: string[];
};

export interface ArtifactV2 {
  id: string;
  contents: (ArtifactMarkdownContent | ArtifactCodeContent)[];
  currentContentIndex: number;
}

export interface MarkdownBlock {
  id: string;
  content: Array<{
    id: string;
    type: string;
    text: string;
    styles: Record<string, any>;
  }>;
  type: string;
}

export interface ArtifactMarkdownContent {
  index: number;
  blocks: MarkdownBlock[];
  title: string;
  type: "text";
}

export interface ArtifactCodeContent {
  index: number;
  code: string;
  title: string;
  type: "code";
  language: ProgrammingLanguageOptions;
}

export interface Highlight {
  /**
   * The index of the first character of the highlighted text
   */
  startCharIndex: number;
  /**
   * The index of the last character of the highlighted text
   */
  endCharIndex: number;
}

export interface NewMarkdownToolResponse {
  blocks: Array<{ block_id?: string; new_text?: string }>;
}

export interface ModelConfig {
  temperature?: number;
  modelProvider: string;
  maxTokens?: number;
  azureConfig?: {
    azureOpenAIApiKey: string;
    azureOpenAIApiInstanceName: string;
    azureOpenAIApiDeploymentName: string;
    azureOpenAIApiVersion: string;
    azureOpenAIBasePath?: string;
  };
}
