import PDFIcon from "@/components/icons/svg/PDFIcon.svg";
import TXTIcon from "@/components/icons/svg/TXTIcon.svg";
import MP4Icon from "@/components/icons/svg/MP4Icon.svg";
import MP3Icon from "@/components/icons/svg/MP3Icon.svg";
import { X } from "lucide-react";
import NextImage from "next/image";
import { Button } from "../ui/button";
import {
  ALLOWED_AUDIO_TYPE_ENDINGS,
  ALLOWED_VIDEO_TYPE_ENDINGS,
} from "@/constants";
import { ContextDocument } from "@opencanvas/shared/types";
import { cn } from "@/lib/utils";

export function UploadedFiles({
  files,
  handleRemoveFile,
  className,
}: {
  files: FileList | ContextDocument[] | undefined;
  handleRemoveFile?: (index: number) => void;
  className?: string;
}) {
  if (!files) return null;

  const filesArr = Array.isArray(files) ? files : Array.from(files);

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {filesArr.map((file, index) => (
        <div
          key={index}
          className="flex items-center gap-2 rounded-md bg-gray-50 px-2 py-1 border-gray-100 border-[1px]"
        >
          {file.type.includes("pdf") && (
            <NextImage alt="PDF icon" src={PDFIcon} width={24} height={24} />
          )}
          {file.type.startsWith("text/") &&
            !ALLOWED_VIDEO_TYPE_ENDINGS.some((ending) =>
              file.name.endsWith(ending)
            ) &&
            !ALLOWED_AUDIO_TYPE_ENDINGS.some((ending) =>
              file.name.endsWith(ending)
            ) && (
              <NextImage alt="TXT icon" src={TXTIcon} width={24} height={24} />
            )}
          {ALLOWED_VIDEO_TYPE_ENDINGS.some((ending) =>
            file.name.endsWith(ending)
          ) && (
            <NextImage alt="MP4 icon" src={MP4Icon} width={24} height={24} />
          )}
          {ALLOWED_AUDIO_TYPE_ENDINGS.some((ending) =>
            file.name.endsWith(ending)
          ) && (
            <NextImage alt="MP3 icon" src={MP3Icon} width={24} height={24} />
          )}
          <span className="text-sm text-gray-600 max-w-[200px] truncate">
            {file.name}
          </span>
          {handleRemoveFile && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-4 w-4 rounded-full p-0"
              onClick={() => handleRemoveFile(index)}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      ))}
    </div>
  );
}
