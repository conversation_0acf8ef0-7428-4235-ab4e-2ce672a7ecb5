1. Project Overview:


   - DeepAudit Canvas is an open-source web application for collaborating with AI agents to create audit work papers and answer audit/accounting questions
   - It's a specialized fork of Open Canvas, refocused for audit professionals
   - It uses a monorepo structure with Turborepo


2. Main Components:


   - apps/agents: Contains the LangGraph agents that power the AI functionality
   - apps/web: The Next.js web application frontend
   - packages/shared: Shared utilities and types used across the project
   - packages/evals: Evaluation tools for testing the agents


3. Agent Architecture:


   - Uses LangGraph from LangChain to create a state graph with various nodes for different functionalities
   - Main nodes include: generateArtifact, updateArtifact, rewriteArtifact, etc.
   - The agent generates text artifacts focused on audit work papers and can update them based on user input




graph TD
    START --> generatePath
    generatePath --> routeNode
    routeNode -->|Generate new artifact| generateArtifact
    routeNode -->|Update existing artifact| updateArtifact
    routeNode -->|Rewrite artifact| rewriteArtifact
    routeNode -->|Rewrite with theme| rewriteArtifactTheme
    routeNode -->|Reply to general input| replyToGeneralInput
    routeNode -->|Update highlighted text| updateHighlightedText

    generateArtifact --> generateFollowup
    updateArtifact --> generateFollowup
    rewriteArtifact --> generateFollowup
    rewriteArtifactTheme --> generateFollowup
    updateHighlightedText --> generateFollowup

    generateFollowup --> cleanState
    replyToGeneralInput --> cleanState

    cleanState -->|First conversation| generateTitle
    cleanState -->|Long conversation| summarizer
    cleanState -->|Normal case| END

    generateTitle --> END
    summarizer --> END




4. Frontend Architecture:


   - Uses Next.js with React
   - Main components include Canvas, ArtifactRenderer, and ContentComposerChatInterface
   - Uses context providers for state management: GraphContext, ThreadProvider, AssistantProvider, UserProvider




5. Key Features:


   - Artifact versioning: Tracks versions of artifacts for time travel
   - Single default assistant: Simplified interface with only one assistant
   - Text focus: Application focused on text content only for audit work papers
   - Live markdown rendering and editing


# DeepAudit Canvas Codebase Overview


DeepAudit Canvas is an open-source web application for collaborating with AI agents to create audit work papers and answer audit/accounting questions. It's a specialized fork of Open Canvas, refocused for audit professionals. Let me walk you through the architecture and key components of this codebase.


## Project Structure


This is a TypeScript monorepo using Turborepo with the following main components:


1. __apps/agents__ - Contains the AI agent logic using LangGraph
2. __apps/web__ - The Next.js frontend application
3. __packages/shared__ - Shared utilities, types, and constants
4. __packages/evals__ - Evaluation tools for testing the agents


## Core Architecture


### Agent System (apps/agents)


The agent system is built using LangGraph (from LangChain) and implements a state graph with various nodes for different functionalities:


Key nodes include:


- __generateArtifact__: Creates new text artifacts focused on audit work papers
- __updateArtifact__: Updates existing artifacts based on user input
- __rewriteArtifact__: Rewrites artifacts with different styles or themes
- __summarizer__: Summarizes long conversations to manage context length


### Frontend (apps/web)


The frontend is built with Next.js and uses a context-based state management system:


Key components:


- __Canvas__: The main UI component with a resizable layout
- __ArtifactRenderer__: Renders text artifacts with editing capabilities
- __ContentComposerChatInterface__: The chat interface for interacting with the AI


graph TD
    UserProvider --> ThreadProvider
    ThreadProvider --> AssistantProvider
    AssistantProvider --> GraphProvider
    GraphProvider --> Canvas

    Canvas --> ArtifactRenderer
    Canvas --> ContentComposerChatInterface

    ArtifactRenderer --> TextRenderer
    ArtifactRenderer --> ActionsToolbar




### State Management


The application uses several React context providers:


- __GraphContext__: Manages communication with the LangGraph server
- __ThreadProvider__: Manages conversation threads
- __AssistantProvider__: Manages assistant configurations
- __UserProvider__: Manages user authentication and preferences


## Data Flow


1. User inputs a message through the chat interface
2. The message is sent to the LangGraph server via the GraphContext
3. The LangGraph server processes the message through the state graph
4. The response is streamed back to the frontend
5. The frontend updates the UI with the new artifact or message


## Key Features


1. __Artifact Versioning__: Tracks versions of artifacts for time travel
2. __Single Default Assistant__: Simplified interface with only one assistant
3. __Text Focus__: Application focused on text content only for audit work papers
4. __Live Rendering__: Real-time rendering of markdown while editing


## Authentication & External Services


The application uses:


- __Supabase__ for authentication
- __LangSmith__ for tracing and observability
- __Various LLM providers__ (OpenAI, Anthropic, etc.) for the AI capabilities



