import <PERSON> from "papaparse";
import * as <PERSON><PERSON><PERSON> from "xlsx";
import { ColumnMapping } from "@/types";

/**
 * Parse a CSV file and return the data as an array of objects
 * @param file The CSV file to parse
 * @returns Promise that resolves to an array of objects
 */
export function parseCSV(file: File): Promise<Record<string, any>[]> {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true,
      complete: (results) => {
        if (results.errors && results.errors.length > 0) {
          reject(new Error(`CSV parsing error: ${results.errors[0].message}`));
        } else {
          resolve(results.data as Record<string, any>[]);
        }
      },
      error: (error) => {
        reject(new Error(`CSV parsing error: ${error.message}`));
      },
    });
  });
}

/**
 * Parse an Excel file and return the data as an array of objects
 * @param file The Excel file to parse
 * @returns Promise that resolves to an array of objects
 */
export function parseExcel(file: File): Promise<Record<string, any>[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          reject(new Error("Failed to read file"));
          return;
        }

        const workbook = XLSX.read(data, { type: "binary" });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON with headers
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        resolve(jsonData as Record<string, any>[]);
      } catch (error) {
        reject(new Error(`Excel parsing error: ${(error as Error).message}`));
      }
    };

    reader.onerror = () => {
      reject(new Error("Failed to read file"));
    };

    reader.readAsBinaryString(file);
  });
}

/**
 * Parse a file based on its type (CSV or Excel)
 * @param file The file to parse
 * @returns Promise that resolves to an array of objects
 */
export async function parseFile(file: File): Promise<Record<string, any>[]> {
  const fileExtension = file.name.split(".").pop()?.toLowerCase();

  if (fileExtension === "csv") {
    return parseCSV(file);
  } else if (["xlsx", "xls"].includes(fileExtension || "")) {
    return parseExcel(file);
  } else {
    throw new Error(`Unsupported file type: ${fileExtension}`);
  }
}

/**
 * Get the column headers from parsed data
 * @param data The parsed data
 * @returns Array of column headers
 */
export function getColumnHeaders(data: Record<string, any>[]): string[] {
  if (!data || data.length === 0) {
    return [];
  }

  // Get all unique keys from all objects in the data array
  const headers = new Set<string>();
  data.forEach((row) => {
    Object.keys(row).forEach((key) => headers.add(key));
  });

  return Array.from(headers);
}

/**
 * Map data from source columns to target columns
 * @param data The source data
 * @param mappings The column mappings
 * @returns Mapped data
 */
export function mapData<T>(
  data: Record<string, any>[],
  mappings: ColumnMapping[]
): T[] {
  return data.map((row) => {
    const mappedRow: Record<string, any> = {};

    mappings.forEach((mapping) => {
      if (mapping.sourceColumn && mapping.sourceColumn !== "none") {
        // Get the value from the source column
        let value = row[mapping.sourceColumn];

        // Handle special cases for specific target columns
        if (mapping.targetColumn === "amount" && typeof value === "string") {
          // Remove currency symbols and commas, then convert to number
          value = parseFloat(value.replace(/[$,]/g, ""));
        } else if (
          (mapping.targetColumn === "balance_date" ||
           mapping.targetColumn === "transaction_date") &&
          value instanceof Date
        ) {
          // Format date as ISO string (YYYY-MM-DD)
          value = value.toISOString().split("T")[0];
        }

        mappedRow[mapping.targetColumn] = value;
      }
    });

    return mappedRow as T;
  });
}
