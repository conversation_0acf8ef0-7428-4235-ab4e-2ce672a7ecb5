"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { UserProvider, useUserContext } from "@/contexts/UserContext";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { RefreshCw, MoreHorizontal, Database, MessageSquare } from "lucide-react";
import { CreateEngagementDialog } from "@/components/engagements/create-engagement-dialog";
import { EditEngagementDialog } from "@/components/engagements/edit-engagement-dialog";
import { DeleteEngagementDialog } from "@/components/engagements/delete-engagement-dialog";
import { SearchFilter } from "@/components/engagements/search-filter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Engagement {
  id: string;
  name: string;
  client_name: string;
  description: string | null;
  created_at: string;
  status: string;
  start_date: string | null;
  end_date: string | null;
  created_by: string;
}

// Dashboard content component that uses UserContext
function DashboardContent() {
  const router = useRouter();
  const { user, loading } = useUserContext();
  const [engagements, setEngagements] = useState<Engagement[]>([]);
  const [filteredEngagements, setFilteredEngagements] = useState<Engagement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  useEffect(() => {
    if (!user && !loading) {
      router.push("/auth/login");
      return;
    }

    if (user) {
      fetchEngagements();
    }
  }, [user, loading]);

  // Apply filters to engagements
  useEffect(() => {
    if (engagements.length === 0) {
      setFilteredEngagements([]);
      return;
    }

    let filtered = [...engagements];

    // Apply search filter
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (engagement) =>
          engagement.name.toLowerCase().includes(lowerSearchTerm) ||
          (engagement.client_name && engagement.client_name.toLowerCase().includes(lowerSearchTerm)) ||
          (engagement.description && engagement.description.toLowerCase().includes(lowerSearchTerm))
      );
    }

    // Apply status filter
    if (statusFilter && statusFilter !== "all") {
      filtered = filtered.filter((engagement) => engagement.status === statusFilter);
    }

    setFilteredEngagements(filtered);
  }, [engagements, searchTerm, statusFilter]);

  const fetchEngagements = async () => {
    try {
      setIsLoading(true);

      // Use the new API endpoint to fetch engagements
      const response = await fetch("/api/engagements");

      if (!response.ok) {
        throw new Error("Failed to fetch engagements");
      }

      const engagementsData = await response.json();
      setEngagements(engagementsData);
    } catch (error) {
      console.error("Failed to fetch engagements:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Callback for search filter
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // Callback for status filter
  const handleStatusFilter = useCallback((status: string) => {
    setStatusFilter(status);
  }, []);

  const handleEngagementSelect = (engagementId: string) => {
    // Store the selected engagement ID in localStorage or query param
    localStorage.setItem("selectedEngagementId", engagementId);

    // Navigate to the chat interface
    router.push("/?engagementId=" + engagementId);
  };

  const handleDataManagement = (engagement: Engagement, e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/data?engagementId=${engagement.id}&engagementName=${encodeURIComponent(engagement.name)}`);
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-3xl font-bold">Your Engagements</h1>
          <div className="flex gap-2">
            <CreateEngagementDialog onEngagementCreated={fetchEngagements} />
            <Button
              variant="outline"
              size="sm"
              onClick={fetchEngagements}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <SearchFilter onSearch={handleSearch} onStatusFilter={handleStatusFilter} />
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-9 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : filteredEngagements.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-xl text-gray-500 mb-4">
              {engagements.length === 0 ? "No engagements found" : "No engagements match your filters"}
            </p>
            {engagements.length === 0 ? (
              <div>
                <p className="text-gray-400 mb-6">
                  Create a new engagement to get started.
                </p>
                <CreateEngagementDialog onEngagementCreated={fetchEngagements} />
              </div>
            ) : (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setStatusFilter("all");
                }}
                className="mt-2"
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEngagements.map((engagement) => (
              <Card
                key={engagement.id}
                className="hover:shadow-md transition-shadow relative"
              >
                {user?.id === engagement.created_by && (
                  <div
                    className="absolute top-2 right-2 z-10"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <div className="cursor-pointer">
                            <EditEngagementDialog
                              engagement={engagement}
                              onEngagementUpdated={fetchEngagements}
                              trigger={<span className="w-full">Edit</span>}
                            />
                          </div>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <div className="cursor-pointer text-red-500">
                            <DeleteEngagementDialog
                              engagement={engagement}
                              onEngagementDeleted={fetchEngagements}
                              trigger={<span className="w-full">Delete</span>}
                            />
                          </div>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
                <div onClick={() => handleEngagementSelect(engagement.id)} className="cursor-pointer">
                  <CardHeader>
                    <CardTitle>{engagement.name}</CardTitle>
                    <CardDescription>
                      {engagement.client_name && (
                        <span className="block font-medium">Client: {engagement.client_name}</span>
                      )}
                      <span className="block">Created: {new Date(engagement.created_at).toLocaleDateString()}</span>
                      {engagement.status && (
                        <span className="block mt-1">
                          Status: <span className={`font-medium ${
                            engagement.status === 'Active' ? 'text-green-600' :
                            engagement.status === 'Planning' ? 'text-orange-600' :
                            engagement.status === 'Review' ? 'text-purple-600' :
                            engagement.status === 'Completed' ? 'text-blue-600' :
                            'text-gray-600'
                          }`}>
                            {engagement.status}
                          </span>
                        </span>
                      )}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {engagement.description && (
                        <p className="text-sm text-gray-500">
                          {engagement.description}
                        </p>
                      )}
                      {(engagement.start_date || engagement.end_date) && (
                        <div className="text-xs text-gray-500 flex justify-between">
                          {engagement.start_date && (
                            <span>Start: {new Date(engagement.start_date).toLocaleDateString()}</span>
                          )}
                          {engagement.end_date && (
                            <span>End: {new Date(engagement.end_date).toLocaleDateString()}</span>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-col gap-2">
                    <Button className="w-full flex items-center gap-2" onClick={() => handleEngagementSelect(engagement.id)}>
                      <MessageSquare className="h-4 w-4" />
                      <span>Open Chat</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full flex items-center gap-2"
                      onClick={(e) => handleDataManagement(engagement, e)}
                    >
                      <Database className="h-4 w-4" />
                      <span>Manage Data</span>
                    </Button>
                  </CardFooter>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Export the wrapped component with UserProvider
export default function DashboardPage() {
  return (
    <UserProvider>
      <DashboardContent />
    </UserProvider>
  );
}
