"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/engagements/route";
exports.ids = ["app/api/engagements/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fengagements%2Froute&page=%2Fapi%2Fengagements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fengagements%2Froute.ts&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fengagements%2Froute&page=%2Fapi%2Fengagements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fengagements%2Froute.ts&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ravis_Documents_Projects_NagaRaju_deepaudit_canvas_apps_web_src_app_api_engagements_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/engagements/route.ts */ \"(rsc)/./src/app/api/engagements/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/engagements/route\",\n        pathname: \"/api/engagements\",\n        filename: \"route\",\n        bundlePath: \"app/api/engagements/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\engagements\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ravis_Documents_Projects_NagaRaju_deepaudit_canvas_apps_web_src_app_api_engagements_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/engagements/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1hcHAtbG9hZGVyLmpzP25hbWU9YXBwJTJGYXBpJTJGZW5nYWdlbWVudHMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmVuZ2FnZW1lbnRzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGZW5nYWdlbWVudHMlMkZyb3V0ZS50cyZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDcmF2aXMlNUNEb2N1bWVudHMlNUNQcm9qZWN0cyU1Q05hZ2FSYWp1JTVDZGVlcGF1ZGl0LWNhbnZhcyU1Q2FwcHMlNUN3ZWIlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q3JhdmlzJTVDRG9jdW1lbnRzJTVDUHJvamVjdHMlNUNOYWdhUmFqdSU1Q2RlZXBhdWRpdC1jYW52YXMlNUNhcHBzJTVDd2ViJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUNrRTtBQUMvSTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8/YjMyYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxyYXZpc1xcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcTmFnYVJhanVcXFxcZGVlcGF1ZGl0LWNhbnZhc1xcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxlbmdhZ2VtZW50c1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvZW5nYWdlbWVudHMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9lbmdhZ2VtZW50c1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvZW5nYWdlbWVudHMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxyYXZpc1xcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcTmFnYVJhanVcXFxcZGVlcGF1ZGl0LWNhbnZhc1xcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxlbmdhZ2VtZW50c1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvZW5nYWdlbWVudHMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fengagements%2Froute&page=%2Fapi%2Fengagements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fengagements%2Froute.ts&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/engagements/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/engagements/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/verify_user_server */ \"(rsc)/./src/lib/supabase/verify_user_server.ts\");\n\n\n\n// GET - Fetch all engagements (with optional filtering)\nasync function GET(req) {\n    try {\n        // Verify user is authenticated\n        const authRes = await (0,_lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__.verifyUserAuthenticated)();\n        if (!authRes?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const userId = authRes.user.id;\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Get query parameters for filtering\n        const url = new URL(req.url);\n        const searchQuery = url.searchParams.get(\"search\") || \"\";\n        const statusFilter = url.searchParams.get(\"status\") || \"\";\n        // First try to get engagements through the junction table\n        const { data: junctionData, error: junctionError } = await supabase.from(\"engagement_users\").select(`\n        engagement:engagement_id(\n          id,\n          name,\n          description,\n          created_at,\n          client_name,\n          status,\n          start_date,\n          end_date,\n          created_by\n        )\n      `).eq(\"user_id\", userId);\n        let engagements = [];\n        if (!junctionError && junctionData && junctionData.length > 0) {\n            // Transform the data to match the Engagement interface\n            engagements = junctionData.map((item)=>item.engagement).filter(Boolean);\n        }\n        // If no results from junction table, try to find engagements where the user is the creator\n        if (engagements.length === 0) {\n            const { data: creatorEngagements, error: creatorError } = await supabase.from(\"engagements\").select(\"id, name, description, created_at, client_name, status, start_date, end_date, created_by\").eq(\"created_by\", userId);\n            if (!creatorError && creatorEngagements) {\n                engagements = creatorEngagements;\n            }\n        }\n        // Apply filters if provided\n        if (searchQuery) {\n            const lowerSearchQuery = searchQuery.toLowerCase();\n            engagements = engagements.filter((engagement)=>engagement.name.toLowerCase().includes(lowerSearchQuery) || engagement.client_name && engagement.client_name.toLowerCase().includes(lowerSearchQuery) || engagement.description && engagement.description.toLowerCase().includes(lowerSearchQuery));\n        }\n        if (statusFilter && statusFilter !== \"all\") {\n            engagements = engagements.filter((engagement)=>engagement.status === statusFilter);\n        }\n        // Sort by created_at (newest first)\n        engagements.sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(engagements);\n    } catch (error) {\n        console.error(\"Error fetching engagements:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch engagements\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create a new engagement\nasync function POST(req) {\n    try {\n        // Verify user is authenticated\n        const authRes = await (0,_lib_supabase_verify_user_server__WEBPACK_IMPORTED_MODULE_2__.verifyUserAuthenticated)();\n        if (!authRes?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const userId = authRes.user.id;\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Parse request body\n        const { name, client_name, description, start_date, end_date, status } = await req.json();\n        // Validate required fields\n        if (!name || !client_name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Name and client name are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Create new engagement\n        const { data: engagement, error } = await supabase.from(\"engagements\").insert({\n            name,\n            client_name,\n            description,\n            start_date,\n            end_date,\n            status: status || \"Active\",\n            created_by: userId,\n            organization_id: userId\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating engagement:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to create engagement\"\n            }, {\n                status: 500\n            });\n        }\n        // Add the creator to the engagement_users junction table\n        const { error: junctionError } = await supabase.from(\"engagement_users\").insert({\n            engagement_id: engagement.id,\n            user_id: userId,\n            added_by: userId\n        });\n        if (junctionError) {\n            console.error(\"Error adding user to engagement:\", junctionError);\n        // We don't return an error here as the engagement was created successfully\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(engagement, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating engagement:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create engagement\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/engagements/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/../../node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/../../node_modules/next/dist/api/headers.js\");\n\n\nfunction createClient() {\n    if (false) {}\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zkxqdklfxrlgnivamfaa.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpreHFka2xmeHJsZ25pdmFtZmFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4NTc5NTMsImV4cCI6MjA2MjQzMzk1M30.uk5huCuz1ywzZmD2dsCg2AWBRh8TvsHd1X7R8TozALE\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/verify_user_server.ts":
/*!************************************************!*\
  !*** ./src/lib/supabase/verify_user_server.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   verifyUserAuthenticated: () => (/* binding */ verifyUserAuthenticated)\n/* harmony export */ });\n/* harmony import */ var _server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\nasync function verifyUserAuthenticated() {\n    const supabase = (0,_server__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    const { data: { user } } = await supabase.auth.getUser();\n    const { data: { session } } = await supabase.auth.getSession();\n    if (!user || !session) {\n        return undefined;\n    }\n    return {\n        user,\n        session\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3ZlcmlmeV91c2VyX3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUN3QztBQUVqQyxlQUFlQztJQUdwQixNQUFNQyxXQUFXRixxREFBWUE7SUFDN0IsTUFBTSxFQUNKRyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUNmLEdBQUcsTUFBTUYsU0FBU0csSUFBSSxDQUFDQyxPQUFPO0lBQy9CLE1BQU0sRUFDSkgsTUFBTSxFQUFFSSxPQUFPLEVBQUUsRUFDbEIsR0FBRyxNQUFNTCxTQUFTRyxJQUFJLENBQUNHLFVBQVU7SUFDbEMsSUFBSSxDQUFDSixRQUFRLENBQUNHLFNBQVM7UUFDckIsT0FBT0U7SUFDVDtJQUNBLE9BQU87UUFBRUw7UUFBTUc7SUFBUTtBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9saWIvc3VwYWJhc2UvdmVyaWZ5X3VzZXJfc2VydmVyLnRzP2JiNDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2Vzc2lvbiwgVXNlciB9IGZyb20gXCJAc3VwYWJhc2Uvc3VwYWJhc2UtanNcIjtcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gXCIuL3NlcnZlclwiO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5VXNlckF1dGhlbnRpY2F0ZWQoKTogUHJvbWlzZTxcbiAgeyB1c2VyOiBVc2VyOyBzZXNzaW9uOiBTZXNzaW9uIH0gfCB1bmRlZmluZWRcbj4ge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuICBjb25zdCB7XG4gICAgZGF0YTogeyB1c2VyIH0sXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcbiAgY29uc3Qge1xuICAgIGRhdGE6IHsgc2Vzc2lvbiB9LFxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XG4gIGlmICghdXNlciB8fCAhc2Vzc2lvbikge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cbiAgcmV0dXJuIHsgdXNlciwgc2Vzc2lvbiB9O1xufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInZlcmlmeVVzZXJBdXRoZW50aWNhdGVkIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImF1dGgiLCJnZXRVc2VyIiwic2Vzc2lvbiIsImdldFNlc3Npb24iLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/verify_user_server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fengagements%2Froute&page=%2Fapi%2Fengagements%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fengagements%2Froute.ts&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();