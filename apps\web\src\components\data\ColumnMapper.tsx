"use client";

import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ColumnMapping } from "@/types";
import { AlertCircle } from "lucide-react";

interface ColumnMapperProps {
  sourceColumns: string[];
  targetColumns: { name: string; required: boolean }[];
  onChange: (mappings: ColumnMapping[]) => void;
}

export function ColumnMapper({
  sourceColumns,
  targetColumns,
  onChange,
}: ColumnMapperProps) {
  const [mappings, setMappings] = useState<ColumnMapping[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize mappings with empty values only once when columns are available
  useEffect(() => {
    if (sourceColumns.length > 0 && targetColumns.length > 0 && mappings.length === 0) {
      const initialMappings = targetColumns.map((target) => ({
        sourceColumn: "none",
        targetColumn: target.name,
        required: target.required,
      }));
      setMappings(initialMappings);
    }
  }, [sourceColumns, targetColumns, mappings.length]);

  // Auto-map columns with exact matches
  useEffect(() => {
    if (sourceColumns.length > 0 && targetColumns.length > 0 && mappings.length > 0) {
      const newMappings = [...mappings];
      let hasChanges = false;

      targetColumns.forEach((target, index) => {
        // Skip if already mapped to something other than "none"
        if (newMappings[index]?.sourceColumn && newMappings[index]?.sourceColumn !== "none") return;

        // Try to find exact match (case insensitive)
        const exactMatch = sourceColumns.find(
          (src) => src.toLowerCase() === target.name.toLowerCase()
        );
        if (exactMatch) {
          newMappings[index] = {
            ...newMappings[index],
            sourceColumn: exactMatch,
          };
          hasChanges = true;
        }
      });

      if (hasChanges) {
        setMappings(newMappings);
        validateMappings(newMappings);
      }
    }
  }, [sourceColumns, targetColumns, mappings]);

  // Validate mappings and notify parent component
  const validateMappings = (mappingsToValidate: ColumnMapping[]) => {
    const newErrors: Record<string, string> = {};

    // Check for required fields
    mappingsToValidate.forEach((mapping) => {
      if (mapping.required && (!mapping.sourceColumn || mapping.sourceColumn === "none")) {
        newErrors[mapping.targetColumn] = "This field is required";
      }
    });

    // Check for duplicate source columns
    const sourceColumnCounts: Record<string, number> = {};
    mappingsToValidate.forEach((mapping) => {
      if (mapping.sourceColumn && mapping.sourceColumn !== "none") {
        sourceColumnCounts[mapping.sourceColumn] =
          (sourceColumnCounts[mapping.sourceColumn] || 0) + 1;
      }
    });

    mappingsToValidate.forEach((mapping) => {
      if (
        mapping.sourceColumn &&
        sourceColumnCounts[mapping.sourceColumn] > 1
      ) {
        newErrors[mapping.targetColumn] =
          "This source column is mapped multiple times";
      }
    });

    setErrors(newErrors);

    // Always notify parent with the current mappings, even if there are errors
    // This allows the parent to see the current state of mappings
    onChange(mappingsToValidate);
  };

  const handleMappingChange = (targetColumn: string, sourceColumn: string) => {
    console.log(`Mapping change: ${targetColumn} -> ${sourceColumn}`);

    // Create a new array to ensure React detects the change
    const newMappings = mappings.map((mapping) =>
      mapping.targetColumn === targetColumn
        ? { ...mapping, sourceColumn }
        : mapping
    );

    // Update the state
    setMappings(newMappings);

    // Validate and notify parent
    validateMappings(newMappings);
  };

  if (mappings.length === 0) {
    return <div>Loading column mapper...</div>;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">Map Columns</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mappings.map((mapping, index) => (
            <div key={mapping.targetColumn} className="space-y-2">
              <div className="flex items-center gap-2">
                <Label htmlFor={`mapping-${index}`}>
                  {mapping.targetColumn}
                </Label>
                {mapping.required && (
                  <Badge variant="outline" className="text-xs">
                    Required
                  </Badge>
                )}
              </div>
              <Select
                value={mapping.sourceColumn}
                onValueChange={(value) =>
                  handleMappingChange(mapping.targetColumn, value)
                }
              >
                <SelectTrigger
                  id={`mapping-${index}`}
                  className={
                    errors[mapping.targetColumn] ? "border-destructive" : ""
                  }
                >
                  <SelectValue placeholder="Select a column" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {sourceColumns.map((column) => (
                    <SelectItem key={column} value={column}>
                      {column}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors[mapping.targetColumn] && (
                <div className="flex items-center text-destructive text-sm mt-1">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors[mapping.targetColumn]}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
