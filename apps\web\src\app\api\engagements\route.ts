import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { verifyUserAuthenticated } from "@/lib/supabase/verify_user_server";

// GET - Fetch all engagements (with optional filtering)
export async function GET(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const supabase = createClient();

    // Get query parameters for filtering
    const url = new URL(req.url);
    const searchQuery = url.searchParams.get("search") || "";
    const statusFilter = url.searchParams.get("status") || "";

    // First try to get engagements through the junction table
    const { data: junctionData, error: junctionError } = await supabase
      .from("engagement_users")
      .select(`
        engagement:engagement_id(
          id,
          name,
          description,
          created_at,
          client_name,
          status,
          start_date,
          end_date,
          created_by
        )
      `)
      .eq("user_id", userId);

    let engagements: any[] = [];

    if (!junctionError && junctionData && junctionData.length > 0) {
      // Transform the data to match the Engagement interface
      engagements = junctionData
        .map(item => item.engagement)
        .filter(Boolean);
    }

    // If no results from junction table, try to find engagements where the user is the creator
    if (engagements.length === 0) {
      const { data: creatorEngagements, error: creatorError } = await supabase
        .from("engagements")
        .select("id, name, description, created_at, client_name, status, start_date, end_date, created_by")
        .eq("created_by", userId);

      if (!creatorError && creatorEngagements) {
        engagements = creatorEngagements;
      }
    }

    // Apply filters if provided
    if (searchQuery) {
      const lowerSearchQuery = searchQuery.toLowerCase();
      engagements = engagements.filter(
        (engagement) =>
          engagement.name.toLowerCase().includes(lowerSearchQuery) ||
          (engagement.client_name && engagement.client_name.toLowerCase().includes(lowerSearchQuery)) ||
          (engagement.description && engagement.description.toLowerCase().includes(lowerSearchQuery))
      );
    }

    if (statusFilter && statusFilter !== "all") {
      engagements = engagements.filter(
        (engagement) => engagement.status === statusFilter
      );
    }

    // Sort by created_at (newest first)
    engagements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    return NextResponse.json(engagements);
  } catch (error) {
    console.error("Error fetching engagements:", error);
    return NextResponse.json(
      { error: "Failed to fetch engagements" },
      { status: 500 }
    );
  }
}

// POST - Create a new engagement
export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const supabase = createClient();

    // Parse request body
    const { name, client_name, description, start_date, end_date, status } = await req.json();

    // Validate required fields
    if (!name || !client_name) {
      return NextResponse.json(
        { error: "Name and client name are required" },
        { status: 400 }
      );
    }

    // Create new engagement
    const { data: engagement, error } = await supabase
      .from("engagements")
      .insert({
        name,
        client_name,
        description,
        start_date,
        end_date,
        status: status || "Active",
        created_by: userId,
        organization_id: userId, // Using userId as organization_id for now
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating engagement:", error);
      return NextResponse.json(
        { error: "Failed to create engagement" },
        { status: 500 }
      );
    }

    // Add the creator to the engagement_users junction table
    const { error: junctionError } = await supabase
      .from("engagement_users")
      .insert({
        engagement_id: engagement.id,
        user_id: userId,
        added_by: userId,
      });

    if (junctionError) {
      console.error("Error adding user to engagement:", junctionError);
      // We don't return an error here as the engagement was created successfully
    }

    return NextResponse.json(engagement, { status: 201 });
  } catch (error) {
    console.error("Error creating engagement:", error);
    return NextResponse.json(
      { error: "Failed to create engagement" },
      { status: 500 }
    );
  }
}
