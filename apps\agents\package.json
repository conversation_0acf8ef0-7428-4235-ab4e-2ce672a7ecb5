{"name": "@opencanvas/agents", "author": "<PERSON><PERSON>", "repository": "https://github.com/langchain-ai/open-canvas", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*"], "license": "MIT", "private": true, "scripts": {"dev": "@langchain/langgraph-cli dev --port 54367 --config ../../langgraph.json", "build": "yarn clean && tsc", "clean": "rimraf ./dist .turbo", "format": "prettier --config .prettierrc --write \"src\"", "lint": "eslint src", "lint:fix": "eslint src --fix"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@langchain/anthropic": "^0.3.12", "@langchain/community": "^0.3.28", "@langchain/core": "^0.3.38", "@langchain/exa": "^0.1.0", "@langchain/google-genai": "^0.1.7", "@langchain/groq": "^0.1.3", "@langchain/langgraph": "^0.2.41", "@langchain/langgraph-cli": "^0.0.35", "@langchain/langgraph-sdk": "^0.0.37", "@langchain/ollama": "^0.1.4", "@langchain/openai": "^0.4.2", "@mendable/firecrawl-js": "1.10.1", "@opencanvas/shared": "*", "@supabase/supabase-js": "^2.45.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "exa-js": "^1.4.10", "framer-motion": "^11.11.9", "groq-sdk": "^0.13.0", "langchain": "^0.3.14", "langsmith": "^0.3.3", "lodash": "^4.17.21", "pdf-parse": "^1.1.1", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/eslint__js": "^8.42.3", "@types/lodash": "^4.17.12", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.8.1", "eslint": "^8", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.3.3", "rimraf": "^5.0.5", "tsx": "^4.19.1", "turbo": "latest", "typescript": "^5", "typescript-eslint": "^8.8.1", "vitest": "^3.0.4"}}