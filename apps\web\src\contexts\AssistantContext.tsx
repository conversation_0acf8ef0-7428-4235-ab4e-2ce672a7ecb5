import { useToast } from "@/hooks/use-toast";
import { Assistant } from "@langchain/langgraph-sdk";
import { ContextDocument } from "@opencanvas/shared/types";
import {
  createContext,
  ReactNode,
  useContext,
  useState,
} from "react";
import { createClient } from "@/hooks/utils";
import { getCookie, removeCookie } from "@/lib/cookies";
import { ASSISTANT_ID_COOKIE } from "@/constants";

type AssistantContentType = {
  defaultAssistant: Assistant | undefined;
  isLoadingAssistant: boolean;
  getOrCreateDefaultAssistant: (userId: string) => Promise<void>;
};

export type AssistantTool = {
  /**
   * The name of the tool
   */
  name: string;
  /**
   * The tool's description.
   */
  description: string;
  /**
   * JSON Schema for the parameters of the tool.
   */
  parameters: Record<string, any>;
};

export interface CreateAssistantFields {
  iconData?: {
    /**
     * The name of the Lucide icon to use for the assistant.
     * @default "User"
     */
    iconName: string;
    /**
     * The hex color code to use for the icon.
     */
    iconColor: string;
  };
  /**
   * The name of the assistant.
   */
  name: string;
  /**
   * An optional description of the assistant, provided by the user/
   */
  description?: string;
  /**
   * The tools the assistant has access to.
   */
  tools?: Array<AssistantTool>;
  /**
   * An optional system prompt to prefix all generations with.
   */
  systemPrompt?: string;
  is_default?: boolean;
  /**
   * The documents to include in the LLMs context.
   */
  documents?: ContextDocument[];
}

export type CreateCustomAssistantArgs = {
  newAssistant: CreateAssistantFields;
  userId: string;
  successCallback?: (id: string) => void;
};

export type EditCustomAssistantArgs = {
  editedAssistant: CreateAssistantFields;
  assistantId: string;
  userId: string;
};

const AssistantContext = createContext<AssistantContentType | undefined>(
  undefined
);

export function AssistantProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [isLoadingAssistant, setIsLoadingAssistant] = useState(false);
  const [defaultAssistant, setDefaultAssistant] = useState<Assistant>();

  /**
   * Creates or retrieves the default assistant for the user
   */
  const getOrCreateDefaultAssistant = async (userId: string) => {
    if (defaultAssistant) {
      return;
    }

    setIsLoadingAssistant(true);
    const client = createClient();

    try {
      // Check for legacy cookie
      const assistantIdCookie = getCookie(ASSISTANT_ID_COOKIE);
      if (assistantIdCookie) {
        try {
          // Get the assistant from the cookie
          await client.assistants.get(assistantIdCookie);

          // Update it to be the default assistant
          const updatedAssistant = await client.assistants.update(assistantIdCookie, {
            name: "DeepAudit Assistant",
            graphId: "agent",
            metadata: {
              user_id: userId,
              is_default: true,
              iconData: {
                iconName: "FileSpreadsheet",
                iconColor: "#2563EB",
              },
              description: "Your audit and accounting assistant for creating work papers and answering professional questions.",
            },
            config: {
              configurable: {
                systemPrompt: "You are DeepAudit Assistant, an AI specialized in audit and accounting. Your primary purpose is to help auditors create professional work papers, test of details documentation, and other audit documentation. You can also answer questions about accounting standards, audit methodologies, and financial reporting requirements. Always maintain a professional tone appropriate for audit and accounting contexts. When creating work papers, follow standard audit documentation practices with clear sections for Purpose, Scope, Methodology, Findings, and Conclusion. For test of details work papers, include sample selection methodology, testing procedures, results, and conclusions. Reference relevant accounting standards (IFRS, GAAP, ISA, etc.) when appropriate. Format financial data in clear tables with proper alignment of numbers."
              },
            },
          });

          setDefaultAssistant(updatedAssistant);
          removeCookie(ASSISTANT_ID_COOKIE);
          setIsLoadingAssistant(false);
          return;
        } catch (e) {
          console.error("Failed to get assistant from cookie", e);
          removeCookie(ASSISTANT_ID_COOKIE);
        }
      }

      // Search for default assistant
      const userAssistants = await client.assistants.search({
        graphId: "agent",
        metadata: {
          user_id: userId,
          is_default: true,
        },
        limit: 1,
      });

      if (userAssistants.length > 0) {
        // Found a default assistant
        setDefaultAssistant(userAssistants[0]);
        setIsLoadingAssistant(false);
        return;
      }

      // No default assistant found, create one
      const createdAssistant = await client.assistants.create({
        graphId: "agent",
        name: "DeepAudit Assistant",
        metadata: {
          user_id: userId,
          is_default: true,
          iconData: {
            iconName: "FileSpreadsheet",
            iconColor: "#2563EB",
          },
          description: "Your audit and accounting assistant for creating work papers and answering professional questions.",
        },
        config: {
          configurable: {
            systemPrompt: "You are DeepAudit Assistant, an AI specialized in audit and accounting. Your primary purpose is to help auditors create professional work papers, test of details documentation, and other audit documentation. You can also answer questions about accounting standards, audit methodologies, and financial reporting requirements. Always maintain a professional tone appropriate for audit and accounting contexts. When creating work papers, follow standard audit documentation practices with clear sections for Purpose, Scope, Methodology, Findings, and Conclusion. For test of details work papers, include sample selection methodology, testing procedures, results, and conclusions. Reference relevant accounting standards (IFRS, GAAP, ISA, etc.) when appropriate. Format financial data in clear tables with proper alignment of numbers."
          },
        },
        ifExists: "do_nothing",
      });

      setDefaultAssistant(createdAssistant);
    } catch (e) {
      console.error("Failed to get or create default assistant", e);
      toast({
        title: "Failed to initialize assistant",
        description: "Please try again later.",
      });
    } finally {
      setIsLoadingAssistant(false);
    }
  };

  const contextValue: AssistantContentType = {
    defaultAssistant,
    isLoadingAssistant,
    getOrCreateDefaultAssistant,
  };

  return (
    <AssistantContext.Provider value={contextValue}>
      {children}
    </AssistantContext.Provider>
  );
}

export function useAssistantContext() {
  const context = useContext(AssistantContext);
  if (context === undefined) {
    throw new Error(
      "useAssistantContext must be used within a AssistantProvider"
    );
  }
  return context;
}

// For backward compatibility with existing code
export interface CreateAssistantFields {
  iconData?: {
    iconName: string;
    iconColor: string;
  };
  name: string;
  description?: string;
  tools?: Array<AssistantTool>;
  systemPrompt?: string;
  is_default?: boolean;
  documents?: ContextDocument[];
}
