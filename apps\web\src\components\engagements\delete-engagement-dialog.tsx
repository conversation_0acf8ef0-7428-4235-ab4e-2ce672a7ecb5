"use client";

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Trash2 } from "lucide-react";

interface Engagement {
  id: string;
  name: string;
}

interface DeleteEngagementDialogProps {
  engagement: Engagement;
  onEngagementDeleted: () => void;
  trigger?: React.ReactNode;
}

export function DeleteEngagementDialog({
  engagement,
  onEngagementDeleted,
  trigger,
}: DeleteEngagementDialogProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsLoading(true);

    try {
      const response = await fetch(`/api/engagements/${engagement.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete engagement");
      }

      toast({
        title: "Engagement deleted",
        description: `${engagement.name} has been deleted successfully.`,
      });

      // Close dialog
      setOpen(false);

      // Refresh engagements list
      onEngagementDeleted();
    } catch (error) {
      console.error("Error deleting engagement:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete engagement",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const defaultTrigger = (
    <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50">
      <Trash2 className="h-4 w-4" />
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger ? (
        <div onClick={(e) => e.stopPropagation()}>{trigger}</div>
      ) : (
        <div onClick={(e) => e.stopPropagation()}>{defaultTrigger}</div>
      )}
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Engagement</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the engagement &quot;{engagement.name}&quot;? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="mt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? "Deleting..." : "Delete Engagement"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
