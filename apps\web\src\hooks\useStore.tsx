import {
  ContextDocument,
} from "@opencanvas/shared/types";
import { useToast } from "./use-toast";
import { Item } from "@langchain/langgraph";
import { CONTEXT_DOCUMENTS_NAMESPACE } from "@opencanvas/shared/constants";

export function useStore() {
  const { /* toast */ } = useToast();



  const putContextDocuments = async ({
    assistantId,
    documents,
  }: {
    assistantId: string;
    documents: ContextDocument[];
  }): Promise<void> => {
    try {
      const res = await fetch("/api/store/put", {
        method: "POST",
        body: JSON.stringify({
          namespace: CONTEXT_DOCUMENTS_NAMESPACE,
          key: assistantId,
          value: {
            documents,
          },
        }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!res.ok) {
        throw new Error(
          "Failed to put context documents" + res.statusText + res.status
        );
      }
    } catch (e) {
      console.error("Failed to put context documents.\n", e);
    }
  };

  const getContextDocuments = async (
    assistantId: string
  ): Promise<ContextDocument[] | undefined> => {
    const res = await fetch("/api/store/get", {
      method: "POST",
      body: JSON.stringify({
        namespace: CONTEXT_DOCUMENTS_NAMESPACE,
        key: assistantId,
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      console.error(
        "Failed to get context documents",
        res.statusText,
        res.status
      );
      return undefined;
    }

    const { item }: { item: Item | null } = await res.json();
    if (!item?.value?.documents) {
      return undefined;
    }

    return item?.value?.documents;
  };

  return {
    putContextDocuments,
    getContextDocuments,
  };
}
