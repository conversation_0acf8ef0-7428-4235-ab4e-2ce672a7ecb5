import { z } from "zod";

export const ARTIFACT_TOOL_SCHEMA = z.object({
  type: z
    .literal("text")
    .describe("The content type of the audit work paper or accounting document generated."),
  artifact: z.string().describe("The content of the audit work paper or accounting document to generate. Should follow professional audit documentation standards with clear sections."),
  title: z
    .string()
    .describe(
      "A professional, descriptive title for the audit work paper or accounting document. Should clearly indicate the purpose or subject of the document (e.g., 'Accounts Receivable Test of Details', 'Revenue Recognition Analysis')."
    ),
});
