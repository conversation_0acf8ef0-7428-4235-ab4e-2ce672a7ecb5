{"name": "open_canvas", "author": "<PERSON><PERSON>", "homepage": "https://opencanvas.langchain.com", "repository": "https://github.com/langchain-ai/open-canvas", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo build", "turbo:command": "turbo", "format": "turbo format", "lint": "turbo lint", "lint:fix": "turbo lint:fix"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "prettier": "^3.3.3", "tsx": "^4.19.1", "turbo": "latest", "typescript": "^5"}, "resolutions": {"@langchain/core": "^0.3.38"}, "dependencies": {"langchain": "^0.3.23"}}