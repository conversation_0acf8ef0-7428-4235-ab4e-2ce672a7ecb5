import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { verifyUserAuthenticated } from "@/lib/supabase/verify_user_server";

// GET - Fetch a specific engagement by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const engagementId = params.id;
    const supabase = createClient();

    // First check if the user has access to this engagement
    const { data: accessData, error: accessError } = await supabase
      .from("engagement_users")
      .select("*")
      .eq("engagement_id", engagementId)
      .eq("user_id", userId)
      .maybeSingle();

    // If not in junction table, check if user is the creator
    let hasAccess = !!accessData;
    if (!hasAccess) {
      const { data: engagementData, error: engagementError } = await supabase
        .from("engagements")
        .select("*")
        .eq("id", engagementId)
        .eq("created_by", userId)
        .maybeSingle();

      hasAccess = !!engagementData;
    }

    if (!hasAccess) {
      return NextResponse.json(
        { error: "You don't have access to this engagement" },
        { status: 403 }
      );
    }

    // Fetch the engagement details
    const { data: engagement, error } = await supabase
      .from("engagements")
      .select("*")
      .eq("id", engagementId)
      .single();

    if (error) {
      console.error("Error fetching engagement:", error);
      return NextResponse.json(
        { error: "Failed to fetch engagement" },
        { status: 500 }
      );
    }

    return NextResponse.json(engagement);
  } catch (error) {
    console.error("Error fetching engagement:", error);
    return NextResponse.json(
      { error: "Failed to fetch engagement" },
      { status: 500 }
    );
  }
}

// PUT - Update an engagement
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const engagementId = params.id;
    const supabase = createClient();

    // Check if the user has access to update this engagement (must be creator)
    const { data: engagement, error: fetchError } = await supabase
      .from("engagements")
      .select("*")
      .eq("id", engagementId)
      .single();

    if (fetchError) {
      console.error("Error fetching engagement:", fetchError);
      return NextResponse.json(
        { error: "Failed to fetch engagement" },
        { status: 500 }
      );
    }

    if (engagement.created_by !== userId) {
      return NextResponse.json(
        { error: "Only the creator can update this engagement" },
        { status: 403 }
      );
    }

    // Parse request body
    const { name, client_name, description, start_date, end_date, status } = await req.json();

    // Validate required fields
    if (!name || !client_name) {
      return NextResponse.json(
        { error: "Name and client name are required" },
        { status: 400 }
      );
    }

    // Update the engagement
    const { data: updatedEngagement, error } = await supabase
      .from("engagements")
      .update({
        name,
        client_name,
        description,
        start_date,
        end_date,
        status,
        updated_at: new Date().toISOString(),
      })
      .eq("id", engagementId)
      .select()
      .single();

    if (error) {
      console.error("Error updating engagement:", error);
      return NextResponse.json(
        { error: "Failed to update engagement" },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedEngagement);
  } catch (error) {
    console.error("Error updating engagement:", error);
    return NextResponse.json(
      { error: "Failed to update engagement" },
      { status: 500 }
    );
  }
}

// DELETE - Delete an engagement
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const engagementId = params.id;
    const supabase = createClient();

    // Check if the user has access to delete this engagement (must be creator)
    const { data: engagement, error: fetchError } = await supabase
      .from("engagements")
      .select("*")
      .eq("id", engagementId)
      .single();

    if (fetchError) {
      console.error("Error fetching engagement:", fetchError);
      return NextResponse.json(
        { error: "Failed to fetch engagement" },
        { status: 500 }
      );
    }

    if (engagement.created_by !== userId) {
      return NextResponse.json(
        { error: "Only the creator can delete this engagement" },
        { status: 403 }
      );
    }

    // First delete from junction table
    const { error: junctionError } = await supabase
      .from("engagement_users")
      .delete()
      .eq("engagement_id", engagementId);

    if (junctionError) {
      console.error("Error deleting from junction table:", junctionError);
      return NextResponse.json(
        { error: "Failed to delete engagement relationships" },
        { status: 500 }
      );
    }

    // Then delete the engagement
    const { error } = await supabase
      .from("engagements")
      .delete()
      .eq("id", engagementId);

    if (error) {
      console.error("Error deleting engagement:", error);
      return NextResponse.json(
        { error: "Failed to delete engagement" },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting engagement:", error);
    return NextResponse.json(
      { error: "Failed to delete engagement" },
      { status: 500 }
    );
  }
}
