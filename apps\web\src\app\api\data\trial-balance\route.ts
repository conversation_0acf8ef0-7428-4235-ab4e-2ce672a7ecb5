import { NextRequest, NextResponse } from "next/server";
import { verifyUserAuthenticated } from "@/lib/supabase/verify_user_server";
import { uploadTrialBalance } from "@/lib/services/data-service";
import { TrialBalanceEntry } from "@/types";

export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const { entries, engagementId } = await req.json() as {
      entries: TrialBalanceEntry[];
      engagementId: string;
    };

    // Validate request
    if (!entries || !Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json(
        { error: "No trial balance entries provided" },
        { status: 400 }
      );
    }

    if (!engagementId) {
      return NextResponse.json(
        { error: "Engagement ID is required" },
        { status: 400 }
      );
    }

    // Validate required fields in each entry
    const invalidEntries = entries.filter(
      (entry) =>
        !entry.account_code ||
        !entry.account_name ||
        !entry.balance_date ||
        entry.amount === undefined
    );

    if (invalidEntries.length > 0) {
      return NextResponse.json(
        {
          error: "Some entries are missing required fields",
          invalidEntries,
        },
        { status: 400 }
      );
    }

    // Upload trial balance entries
    const result = await uploadTrialBalance(entries, engagementId, userId);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Error uploading trial balance:", error);
    return NextResponse.json(
      { error: `Failed to upload trial balance: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get engagement ID from query params
    const url = new URL(req.url);
    const engagementId = url.searchParams.get("engagementId");

    if (!engagementId) {
      return NextResponse.json(
        { error: "Engagement ID is required" },
        { status: 400 }
      );
    }

    // Get trial balance entries for the engagement
    const supabase = createClient();
    const { data, error } = await supabase
      .from("trial_balance")
      .select("*")
      .eq("engagement_id", engagementId)
      .order("account_code", { ascending: true });

    if (error) {
      console.error("Error fetching trial balance:", error);
      return NextResponse.json(
        { error: `Failed to fetch trial balance: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching trial balance:", error);
    return NextResponse.json(
      { error: `Failed to fetch trial balance: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

// Import at the top of the file
import { createClient } from "@/lib/supabase/server";

export async function DELETE(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get engagement ID from query params
    const url = new URL(req.url);
    const engagementId = url.searchParams.get("engagementId");

    if (!engagementId) {
      return NextResponse.json(
        { error: "Engagement ID is required" },
        { status: 400 }
      );
    }

    // Delete trial balance entries for the engagement
    const supabase = createClient();
    const { error } = await supabase
      .from("trial_balance")
      .delete()
      .eq("engagement_id", engagementId);

    if (error) {
      console.error("Error deleting trial balance:", error);
      return NextResponse.json(
        { error: `Failed to delete trial balance: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, message: "Trial balance deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting trial balance:", error);
    return NextResponse.json(
      { error: `Failed to delete trial balance: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}
