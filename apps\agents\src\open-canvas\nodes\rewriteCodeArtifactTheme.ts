import { LangGraphRunnableConfig } from "@langchain/langgraph";
import {
  OpenCanvasGraphAnnotation,
  OpenCanvasGraphReturnType,
} from "../state.js";

/**
 * This function previously handled code-related features that have been removed from the application.
 * It now throws an error to indicate that these features are no longer supported.
 */
export const rewriteCodeArtifactTheme = async (
  _state: typeof OpenCanvasGraphAnnotation.State,
  _config: LangGraphRunnableConfig
): Promise<OpenCanvasGraphReturnType> => {
  // Code-related features have been removed from the application
  throw new Error("Code-related features are no longer supported");
};
