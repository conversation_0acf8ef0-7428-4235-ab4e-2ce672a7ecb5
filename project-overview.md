# DeepAudit Canvas Project Overview

DeepAudit Canvas is an open-source web application for collaborating with AI agents to create audit work papers and answer audit/accounting questions. It's a specialized fork of Open Canvas, refocused for audit professionals.

## Project Focus

DeepAudit Canvas has been refocused from general content creation to specifically address the needs of audit professionals:

1. **Audit Work Paper Creation**: The application is specialized in creating professional audit documentation including test of details, financial analyses, and accounting memos.
2. **Accounting Questions**: The AI assistant can answer questions related to audit and accounting standards, methodologies, and best practices.
3. **Text-Only Focus**: The application is focused on text content only, removing code generation features to provide a streamlined experience for audit document creation and editing.

## Key Features

- **Single Default Assistant**: The application has been simplified to provide only one default assistant focused on audit work, removing the ability to create multiple assistants.
- **Engagement Management**: Built-in support for organizing work papers by audit engagements using the engagements table in Supabase.
- **Artifact Versioning**: All audit work papers have a "version" tied to them, allowing you to travel back in time and see previous versions of your documentation.
- **Live Markdown Rendering & Editing**: The markdown editor allows you to view the rendered markdown while you're editing, without having to toggle back and forth.
- **Accounting Standards References**: Automatically includes references to relevant accounting standards (IFRS, GAAP, ISA, etc.) when appropriate.
- **Professional Financial Formatting**: Formats financial data in clear tables with proper alignment of numbers.

## Removed Features

To streamline the application and focus on audit work, the following features have been removed:

- **Multiple Assistants**: The ability to create multiple assistants has been removed in favor of a single default assistant.
- **Web Search**: The web search feature has been removed from both backend and frontend of the application.
- **Custom Quick Actions**: Custom quick actions have been removed from both backend and frontend.
- **Code Generation**: Code generation features have been removed to focus on text-only content.
- **Reflections**: The reflections feature has been removed from both prompts and codebase.

## Project Architecture

This is a TypeScript monorepo using Turborepo with the following main components:

1. **apps/agents**: Contains the AI agent logic using LangGraph
2. **apps/web**: The Next.js frontend application
3. **packages/shared**: Shared utilities, types, and constants
4. **packages/evals**: Evaluation tools for testing the agents

### Agent System (apps/agents)

The agent system is built using LangGraph (from LangChain) and implements a state graph with various nodes for different functionalities:

Key nodes include:
- **generateArtifact**: Creates new text artifacts focused on audit work papers
- **updateArtifact**: Updates existing artifacts based on user input
- **rewriteArtifact**: Rewrites artifacts with different styles or themes
- **summarizer**: Summarizes long conversations to manage context length

### Frontend (apps/web)

The frontend is built with Next.js and uses a context-based state management system:

Key components:
- **Canvas**: The main UI component with a resizable layout
- **ArtifactRenderer**: Renders text artifacts with editing capabilities
- **ContentComposerChatInterface**: The chat interface for interacting with the AI

### State Management

The application uses several React context providers:
- **GraphContext**: Manages communication with the LangGraph server
- **ThreadProvider**: Manages conversation threads
- **AssistantProvider**: Manages assistant configurations
- **UserProvider**: Manages user authentication and preferences

## Data Flow

1. User inputs a message through the chat interface
2. The message is sent to the LangGraph server via the GraphContext
3. The LangGraph server processes the message through the state graph
4. The response is streamed back to the frontend
5. The frontend updates the UI with the new artifact or message

## Database Structure

The application uses Supabase for authentication and data storage:

- **Users**: Stores user information and authentication details
- **Engagements**: Stores information about audit engagements
- **Artifacts**: Stores the audit work papers and their versions
- **Threads**: Stores conversation threads between users and the assistant

## External Services

The application integrates with several external services:

- **LLM Providers**: OpenAI, Anthropic, Google, Fireworks for AI capabilities
- **Supabase**: For authentication and database
- **LangSmith**: For tracing and observability of the AI agents

## Future Development

Future development will focus on enhancing the audit-specific capabilities:

- **Enhanced audit templates**: Pre-built templates for common audit work papers
- **Financial data integration**: Ability to import financial data directly from spreadsheets or accounting systems
- **Audit standards library**: Built-in references to accounting standards and audit methodologies
- **Advanced engagement management**: Enhanced features for organizing and managing audit engagements
- **Improved collaboration features**: Better ways to share and collaborate on audit work papers with team members
