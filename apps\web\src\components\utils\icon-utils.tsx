import * as Icons from "lucide-react";
import React from "react";

/**
 * Utility function to get an icon component by name
 * @param iconName The name of the icon to get
 * @returns The icon component
 */
export const getIcon = (iconName?: string) => {
  if (iconName && Icons[iconName as keyof typeof Icons]) {
    return React.createElement(
      Icons[iconName as keyof typeof Icons] as React.ElementType
    );
  }
  return React.createElement(Icons.User);
};
