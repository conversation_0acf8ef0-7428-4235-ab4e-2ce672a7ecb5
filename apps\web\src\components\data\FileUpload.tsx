"use client";

import { useState, useRef, ChangeEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { UploadCloud, X, FileSpreadsheet, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  acceptedFileTypes: string;
  maxSizeInMB?: number;
  className?: string;
}

export function FileUpload({
  onFileSelect,
  acceptedFileTypes,
  maxSizeInMB = 10,
  className,
}: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    validateAndSetFile(selectedFile);
  };

  const validateAndSetFile = (selectedFile: File | undefined) => {
    if (!selectedFile) {
      setError(null);
      setFile(null);
      return;
    }

    // Check file type
    const fileType = selectedFile.type;
    const fileExtension = selectedFile.name.split(".").pop()?.toLowerCase();
    const acceptedTypes = acceptedFileTypes.split(",").map(type => type.trim());
    
    const isValidType = acceptedTypes.some(type => {
      if (type.startsWith(".")) {
        return `.${fileExtension}` === type;
      }
      return fileType === type;
    });

    if (!isValidType) {
      setError(`Invalid file type. Please upload ${acceptedFileTypes}`);
      setFile(null);
      return;
    }

    // Check file size
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (selectedFile.size > maxSizeInBytes) {
      setError(`File size exceeds ${maxSizeInMB}MB limit`);
      setFile(null);
      return;
    }

    setError(null);
    setFile(selectedFile);
    onFileSelect(selectedFile);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFile = e.dataTransfer.files[0];
    validateAndSetFile(droppedFile);
  };

  const handleRemoveFile = () => {
    setFile(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("w-full", className)}>
      <Input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={acceptedFileTypes}
        className="hidden"
      />

      {!file ? (
        <Card
          className={cn(
            "border-2 border-dashed border-gray-300 rounded-lg cursor-pointer",
            isDragging && "border-primary bg-primary/5",
            error && "border-destructive"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleBrowseClick}
        >
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <UploadCloud
              className={cn(
                "h-12 w-12 mb-4",
                isDragging ? "text-primary" : "text-gray-400",
                error && "text-destructive"
              )}
            />
            <h3 className="text-lg font-semibold mb-2">
              {isDragging ? "Drop file here" : "Upload File"}
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Drag and drop or click to browse
            </p>
            <p className="text-xs text-gray-400">
              Accepted formats: {acceptedFileTypes}
            </p>
            <p className="text-xs text-gray-400">
              Max size: {maxSizeInMB}MB
            </p>
            {error && (
              <div className="flex items-center mt-4 text-destructive text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                {error}
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card className="border rounded-lg">
          <CardContent className="flex items-center justify-between py-4">
            <div className="flex items-center">
              <FileSpreadsheet className="h-8 w-8 text-primary mr-3" />
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-xs text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(2)}MB
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRemoveFile}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
