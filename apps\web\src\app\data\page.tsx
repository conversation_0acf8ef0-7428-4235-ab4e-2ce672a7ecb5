"use client";

import { useState, useEffect, Suspense } from "react";
import { UserProvider } from "@/contexts/UserContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { useUserContext } from "@/contexts/UserContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TrialBalanceUpload } from "@/components/data/TrialBalanceUpload";
import { JournalLedgerUpload } from "@/components/data/JournalLedgerUpload";
import { Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

function DataPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading } = useUserContext();
  const [engagementId, setEngagementId] = useState<string>("");
  const [engagementName, setEngagementName] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!user && !loading) {
      router.push("/auth/login");
    }
  }, [user, loading, router]);

  useEffect(() => {
    const id = searchParams.get("engagementId");
    const name = searchParams.get("engagementName");

    if (!id) {
      setError("No engagement selected. Please select an engagement from the dashboard.");
    } else {
      setEngagementId(id);
      if (name) {
        setEngagementName(decodeURIComponent(name));
      }
    }
  }, [searchParams]);

  const handleBackToDashboard = () => {
    router.push("/dashboard");
  };

  const handleUploadComplete = () => {
    // You could refresh data or show a success message here
  };

  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Engagement Data</h1>
          {engagementName && (
            <p className="text-gray-500 mt-1">
              Engagement: {engagementName}
            </p>
          )}
        </div>
        <Button onClick={handleBackToDashboard} variant="outline">
          Back to Dashboard
        </Button>
      </div>

      {error ? (
        <Alert variant="destructive" className="mb-8">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <Tabs defaultValue="trial-balance" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="trial-balance">Trial Balance</TabsTrigger>
            <TabsTrigger value="journal-ledger">Journal Ledger</TabsTrigger>
          </TabsList>
          <TabsContent value="trial-balance">
            <TrialBalanceUpload
              engagementId={engagementId}
              onUploadComplete={handleUploadComplete}
            />
          </TabsContent>
          <TabsContent value="journal-ledger">
            <JournalLedgerUpload
              engagementId={engagementId}
              onUploadComplete={handleUploadComplete}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="flex items-center justify-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
      <p>Loading page...</p>
    </div>
  );
}

export default function DataPage() {
  return (
    <UserProvider>
      <Suspense fallback={<LoadingFallback />}>
        <DataPageContent />
      </Suspense>
    </UserProvider>
  );
}
