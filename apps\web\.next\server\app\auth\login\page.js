/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5Clogin%5C%5Cactions.ts%22%2C%5B%22login%22%5D%5D%5D&__client_imported__=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5Clogin%5C%5Cactions.ts%22%2C%5B%22login%22%5D%5D%5D&__client_imported__=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst actions = {\n'1fbbb49c13fa01403a9b894495b52e9b9d4f23fa': () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/login/actions.ts */ \"(action-browser)/./src/components/auth/login/actions.ts\")).then(mod => mod[\"login\"]),\n}\n\nasync function endpoint(id, ...args) {\n  const action = await actions[id]()\n  return action.apply(null, args)\n}\n\n// Using CJS to avoid this to be tree-shaken away due to unused exports.\nmodule.exports = {\n  '1fbbb49c13fa01403a9b894495b52e9b9d4f23fa': endpoint.bind(null, '1fbbb49c13fa01403a9b894495b52e9b9d4f23fa'),\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcmF2aXMlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q05hZ2FSYWp1JTVDJTVDZGVlcGF1ZGl0LWNhbnZhcyU1QyU1Q2FwcHMlNUMlNUN3ZWIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYXV0aCU1QyU1Q2xvZ2luJTVDJTVDYWN0aW9ucy50cyUyMiUyQyU1QiUyMmxvZ2luJTIyJTVEJTVEJTVEJl9fY2xpZW50X2ltcG9ydGVkX189dHJ1ZSEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0Esa0RBQWtELG1NQUEySjtBQUM3TTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8/MDlmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmNvbnN0IGFjdGlvbnMgPSB7XG4nMWZiYmI0OWMxM2ZhMDE0MDNhOWI4OTQ0OTViNTJlOWI5ZDRmMjNmYSc6ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aXNcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXE5hZ2FSYWp1XFxcXGRlZXBhdWRpdC1jYW52YXNcXFxcYXBwc1xcXFx3ZWJcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcYXV0aFxcXFxsb2dpblxcXFxhY3Rpb25zLnRzXCIpLnRoZW4obW9kID0+IG1vZFtcImxvZ2luXCJdKSxcbn1cblxuYXN5bmMgZnVuY3Rpb24gZW5kcG9pbnQoaWQsIC4uLmFyZ3MpIHtcbiAgY29uc3QgYWN0aW9uID0gYXdhaXQgYWN0aW9uc1tpZF0oKVxuICByZXR1cm4gYWN0aW9uLmFwcGx5KG51bGwsIGFyZ3MpXG59XG5cbi8vIFVzaW5nIENKUyB0byBhdm9pZCB0aGlzIHRvIGJlIHRyZWUtc2hha2VuIGF3YXkgZHVlIHRvIHVudXNlZCBleHBvcnRzLlxubW9kdWxlLmV4cG9ydHMgPSB7XG4gICcxZmJiYjQ5YzEzZmEwMTQwM2E5Yjg5NDQ5NWI1MmU5YjlkNGYyM2ZhJzogZW5kcG9pbnQuYmluZChudWxsLCAnMWZiYmI0OWMxM2ZhMDE0MDNhOWI4OTQ0OTViNTJlOWI5ZDRmMjNmYScpLFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5Clogin%5C%5Cactions.ts%22%2C%5B%22login%22%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNyYXZpcyU1QyU1Q0RvY3VtZW50cyU1QyU1Q1Byb2plY3RzJTVDJTVDTmFnYVJhanUlNUMlNUNkZWVwYXVkaXQtY2FudmFzJTVDJTVDYXBwcyU1QyU1Q3dlYiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2F1dGglNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBa0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ab3BlbmNhbnZhcy93ZWIvP2NhOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxyYXZpc1xcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcTmFnYVJhanVcXFxcZGVlcGF1ZGl0LWNhbnZhc1xcXFxhcHBzXFxcXHdlYlxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNyYXZpcyU1QyU1Q0RvY3VtZW50cyU1QyU1Q1Byb2plY3RzJTVDJTVDTmFnYVJhanUlNUMlNUNkZWVwYXVkaXQtY2FudmFzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNyYXZpcyU1QyU1Q0RvY3VtZW50cyU1QyU1Q1Byb2plY3RzJTVDJTVDTmFnYVJhanUlNUMlNUNkZWVwYXVkaXQtY2FudmFzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcmF2aXMlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q05hZ2FSYWp1JTVDJTVDZGVlcGF1ZGl0LWNhbnZhcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3JhdmlzJTVDJTVDRG9jdW1lbnRzJTVDJTVDUHJvamVjdHMlNUMlNUNOYWdhUmFqdSU1QyU1Q2RlZXBhdWRpdC1jYW52YXMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3JhdmlzJTVDJTVDRG9jdW1lbnRzJTVDJTVDUHJvamVjdHMlNUMlNUNOYWdhUmFqdSU1QyU1Q2RlZXBhdWRpdC1jYW52YXMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcmF2aXMlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q05hZ2FSYWp1JTVDJTVDZGVlcGF1ZGl0LWNhbnZhcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBPQUFtSztBQUNuSztBQUNBLDRPQUFvSztBQUNwSztBQUNBLGtQQUF1SztBQUN2SztBQUNBLGdQQUFzSztBQUN0SztBQUNBLDBQQUEySztBQUMzSztBQUNBLDhRQUFxTCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8/NTAwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHJhdmlzXFxcXERvY3VtZW50c1xcXFxQcm9qZWN0c1xcXFxOYWdhUmFqdVxcXFxkZWVwYXVkaXQtY2FudmFzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aXNcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXE5hZ2FSYWp1XFxcXGRlZXBhdWRpdC1jYW52YXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aXNcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXE5hZ2FSYWp1XFxcXGRlZXBhdWRpdC1jYW52YXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aXNcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXE5hZ2FSYWp1XFxcXGRlZXBhdWRpdC1jYW52YXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxyYXZpc1xcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcTmFnYVJhanVcXFxcZGVlcGF1ZGl0LWNhbnZhc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aXNcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXE5hZ2FSYWp1XFxcXGRlZXBhdWRpdC1jYW52YXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnuqs%5C%5Cdist%5C%5Cadapters%5C%5Cnext%5C%5Capp.js%22%2C%22ids%22%3A%5B%22NuqsAdapter%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnuqs%5C%5Cdist%5C%5Cadapters%5C%5Cnext%5C%5Capp.js%22%2C%22ids%22%3A%5B%22NuqsAdapter%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/nuqs/dist/adapters/next/app.js */ \"(ssr)/../../node_modules/nuqs/dist/adapters/next/app.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNyYXZpcyU1QyU1Q0RvY3VtZW50cyU1QyU1Q1Byb2plY3RzJTVDJTVDTmFnYVJhanUlNUMlNUNkZWVwYXVkaXQtY2FudmFzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3JhdmlzJTVDJTVDRG9jdW1lbnRzJTVDJTVDUHJvamVjdHMlNUMlNUNOYWdhUmFqdSU1QyU1Q2RlZXBhdWRpdC1jYW52YXMlNUMlNUNhcHBzJTVDJTVDd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcmF2aXMlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q05hZ2FSYWp1JTVDJTVDZGVlcGF1ZGl0LWNhbnZhcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q251cXMlNUMlNUNkaXN0JTVDJTVDYWRhcHRlcnMlNUMlNUNuZXh0JTVDJTVDYXBwLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTnVxc0FkYXB0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhNQUF5TCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8/NDgzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk51cXNBZGFwdGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aXNcXFxcRG9jdW1lbnRzXFxcXFByb2plY3RzXFxcXE5hZ2FSYWp1XFxcXGRlZXBhdWRpdC1jYW52YXNcXFxcbm9kZV9tb2R1bGVzXFxcXG51cXNcXFxcZGlzdFxcXFxhZGFwdGVyc1xcXFxuZXh0XFxcXGFwcC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Cnode_modules%5C%5Cnuqs%5C%5Cdist%5C%5Cadapters%5C%5Cnext%5C%5Capp.js%22%2C%22ids%22%3A%5B%22NuqsAdapter%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_login_Login__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/login/Login */ \"(ssr)/./src/components/auth/login/Login.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 9,\n                columnNumber: 27\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_login_Login__WEBPACK_IMPORTED_MODULE_1__.Login, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgvbG9naW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVzRDtBQUNyQjtBQUVsQixTQUFTRTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBS0MsV0FBVTtrQkFDZCw0RUFBQ0gsMkNBQVFBO1lBQUNJLHdCQUFVLDhEQUFDQzswQkFBSTs7Ozs7O3NCQUN2Qiw0RUFBQ04sK0RBQUtBOzs7Ozs7Ozs7Ozs7Ozs7QUFJZCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9hcHAvYXV0aC9sb2dpbi9wYWdlLnRzeD9mNmYzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBMb2dpbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvYXV0aC9sb2dpbi9Mb2dpblwiO1xuaW1wb3J0IHsgU3VzcGVuc2UgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJoLXNjcmVlblwiPlxuICAgICAgPFN1c3BlbnNlIGZhbGxiYWNrPXs8ZGl2PkxvYWRpbmcuLi48L2Rpdj59PlxuICAgICAgICA8TG9naW4gLz5cbiAgICAgIDwvU3VzcGVuc2U+XG4gICAgPC9tYWluPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvZ2luIiwiU3VzcGVuc2UiLCJQYWdlIiwibWFpbiIsImNsYXNzTmFtZSIsImZhbGxiYWNrIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/login/Login.tsx":
/*!*********************************************!*\
  !*** ./src/components/auth/login/Login.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Login: () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/../../node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/dist/api/link.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _user_auth_form_login__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./user-auth-form-login */ \"(ssr)/./src/components/auth/login/user-auth-form-login.tsx\");\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./actions */ \"(ssr)/./src/components/auth/login/actions.ts\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n\n\n\n\n\n\n\n\n\n\nfunction Login() {\n    const [isError, setIsError] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        const error = searchParams.get(\"error\");\n        if (error === \"true\") {\n            setIsError(true);\n            // Remove the error parameter from the URL\n            const newSearchParams = new URLSearchParams(searchParams);\n            newSearchParams.delete(\"error\");\n            router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, {\n                scroll: false\n            });\n        }\n    }, [\n        searchParams,\n        router\n    ]);\n    const onLoginWithEmail = async (input)=>{\n        setIsError(false);\n        await (0,_actions__WEBPACK_IMPORTED_MODULE_6__.login)(input);\n    };\n    const onLoginWithOauth = async (provider)=>{\n        setIsError(false);\n        const client = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_7__.createSupabaseClient)();\n        const currentOrigin =  false ? 0 : \"\";\n        await client.auth.signInWithOAuth({\n            provider,\n            options: {\n                redirectTo: `${currentOrigin}/auth/callback`\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container relative h-full flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                href: \"/auth/signup\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)((0,_ui_button__WEBPACK_IMPORTED_MODULE_4__.buttonVariants)({\n                    variant: \"ghost\"\n                }), \"absolute md:flex hidden right-4 top-4 md:right-8 md:top-8\"),\n                children: \"Signup\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-zinc-900\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-20 flex gap-1 items-center text-lg font-medium\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/lc_logo.jpg\",\n                                width: 36,\n                                height: 36,\n                                alt: \"LangChain Logo\",\n                                className: \"rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            \"Open Canvas\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-2 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold tracking-tight\",\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/auth/signup\",\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)((0,_ui_button__WEBPACK_IMPORTED_MODULE_4__.buttonVariants)({\n                                        variant: \"ghost\"\n                                    }), \"md:hidden flex\"),\n                                    children: \"Signup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_auth_form_login__WEBPACK_IMPORTED_MODULE_5__.UserAuthForm, {\n                            onLoginWithEmail: onLoginWithEmail,\n                            onLoginWithOauth: onLoginWithOauth\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        isError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-500 text-sm text-center\",\n                            children: \"There was an error signing into your account. Please try again.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\Login.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/login/Login.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/login/actions.ts":
/*!**********************************************!*\
  !*** ./src/components/auth/login/actions.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   login: () => (/* binding */ login)
/* harmony export */ });
/* harmony import */ var next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/client/app-call-server */ "(ssr)/../../node_modules/next/dist/client/app-call-server.js");
/* harmony import */ var next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js");



function __build_action__(action, args) {
  return (0,next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__.callServer)(action.$$id, args)
}

/* __next_internal_action_entry_do_not_use__ {"1fbbb49c13fa01403a9b894495b52e9b9d4f23fa":"login"} */ var login = (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__.createServerReference)("1fbbb49c13fa01403a9b894495b52e9b9d4f23fa");




/***/ }),

/***/ "(ssr)/./src/components/auth/login/user-auth-form-login.tsx":
/*!************************************************************!*\
  !*** ./src/components/auth/login/user-auth-form-login.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserAuthForm: () => (/* binding */ UserAuthForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../ui/icons */ \"(ssr)/./src/components/ui/icons.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _ui_password_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../ui/password-input */ \"(ssr)/./src/components/ui/password-input.tsx\");\n/* __next_internal_client_entry_do_not_use__ UserAuthForm auto */ \n\n\n\n\n\n\n\n\nfunction UserAuthForm({ className, onLoginWithEmail, onLoginWithOauth, ...props }) {\n    const [isEmailPasswordLoading, setEmailPasswordIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGoogleLoading, setGoogleIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGithubLoading, setGithubIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPasswordField, setShowPasswordField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isLoading = isEmailPasswordLoading || isGoogleLoading || isGithubLoading;\n    async function onSubmit(event) {\n        event.preventDefault();\n        setEmailPasswordIsLoading(true);\n        await onLoginWithEmail({\n            email,\n            password\n        });\n        setEmailPasswordIsLoading(false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"grid gap-6\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onSubmit,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-1 pb-[2px] px-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"sr-only\",\n                                            htmlFor: \"email\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"email\",\n                                            placeholder: \"<EMAIL>\",\n                                            type: \"email\",\n                                            autoCapitalize: \"none\",\n                                            autoComplete: \"email\",\n                                            autoCorrect: \"off\",\n                                            disabled: isLoading,\n                                            value: email,\n                                            onChange: (e)=>{\n                                                setEmail(e.target.value);\n                                                if (!showPasswordField) {\n                                                    setShowPasswordField(true);\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"overflow-hidden transition-all duration-300 ease-in-out pt-[2px] pb-1 px-1\", showPasswordField ? \"max-h-20 opacity-100\" : \"max-h-0 opacity-0\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"sr-only\",\n                                            htmlFor: \"password\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_password_input__WEBPACK_IMPORTED_MODULE_7__.PasswordInput, {\n                                            id: \"password\",\n                                            autoComplete: \"new-password\",\n                                            autoCorrect: \"off\",\n                                            disabled: isLoading,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            disabled: isLoading,\n                            children: [\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Icons.spinner, {\n                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                \"Login with Email\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"w-full border-t\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center text-xs uppercase\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-background px-2 text-muted-foreground\",\n                            children: \"Or continue with\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: async ()=>{\n                    setGoogleIsLoading(true);\n                    await onLoginWithOauth(\"google\");\n                    setGoogleIsLoading(false);\n                },\n                variant: \"outline\",\n                type: \"button\",\n                disabled: isLoading,\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Icons.spinner, {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Icons.google, {\n                        className: \"mr-2 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    \"Google\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: async ()=>{\n                    setGithubIsLoading(true);\n                    await onLoginWithOauth(\"github\");\n                    setGithubIsLoading(false);\n                },\n                variant: \"outline\",\n                type: \"button\",\n                disabled: isLoading,\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Icons.spinner, {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Icons.gitHub, {\n                        className: \"mr-2 h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    \"GitHub\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\auth\\\\login\\\\user-auth-form-login.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/login/user-auth-form-login.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/icons.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/icons.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Icons: () => (/* binding */ Icons)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Icons = {\n    logo: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 256 256\",\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"256\",\n                    height: \"256\",\n                    fill: \"none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                    lineNumber: 6,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                    x1: \"208\",\n                    y1: \"128\",\n                    x2: \"128\",\n                    y2: \"208\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"16\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                    x1: \"192\",\n                    y1: \"40\",\n                    x2: \"40\",\n                    y2: \"192\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: \"16\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 5,\n            columnNumber: 5\n        }, undefined),\n    twitter: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            ...props,\n            height: \"23\",\n            viewBox: \"0 0 1200 1227\",\n            width: \"23\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M714.163 519.284L1160.89 0H1055.03L667.137 450.887L357.328 0H0L468.492 681.821L0 1226.37H105.866L515.491 750.218L842.672 1226.37H1200L714.137 519.284H714.163ZM569.165 687.828L521.697 619.934L144.011 79.6944H306.615L611.412 515.685L658.88 583.579L1055.08 1150.3H892.476L569.165 687.854V687.828Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 32,\n            columnNumber: 5\n        }, undefined),\n    gitHub: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 438.549 438.549\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"currentColor\",\n                d: \"M409.132 114.573c-19.608-33.596-46.205-60.194-79.798-79.8-33.598-19.607-70.277-29.408-110.063-29.408-39.781 0-76.472 9.804-110.063 29.408-33.596 19.605-60.192 46.204-79.8 79.8C9.803 148.168 0 184.854 0 224.63c0 47.78 13.94 90.745 41.827 128.906 27.884 38.164 63.906 64.572 108.063 79.227 5.14.954 8.945.283 11.419-1.996 2.475-2.282 3.711-5.14 3.711-8.562 0-.571-.049-5.708-.144-15.417a2549.81 2549.81 0 01-.144-25.406l-6.567 1.136c-4.187.767-9.469 1.092-15.846 1-6.374-.089-12.991-.757-19.842-1.999-6.854-1.231-13.229-4.086-19.13-8.559-5.898-4.473-10.085-10.328-12.56-17.556l-2.855-6.57c-1.903-4.374-4.899-9.233-8.992-14.559-4.093-5.331-8.232-8.945-12.419-10.848l-1.999-1.431c-1.332-.951-2.568-2.098-3.711-3.429-1.142-1.331-1.997-2.663-2.568-3.997-.572-1.335-.098-2.43 1.427-3.289 1.525-.859 4.281-1.276 8.28-1.276l5.708.853c3.807.763 8.516 3.042 14.133 6.851 5.614 3.806 10.229 8.754 13.846 14.842 4.38 7.806 9.657 13.754 15.846 17.847 6.184 4.093 12.419 6.136 18.699 6.136 6.28 0 11.704-.476 16.274-1.423 4.565-.952 8.848-2.383 12.847-4.285 1.713-12.758 6.377-22.559 13.988-29.41-10.848-1.14-20.601-2.857-29.264-5.14-8.658-2.286-17.605-5.996-26.835-11.14-9.235-5.137-16.896-11.516-22.985-19.126-6.09-7.614-11.088-17.61-14.987-29.979-3.901-12.374-5.852-26.648-5.852-42.826 0-23.035 7.52-42.637 22.557-58.817-7.044-17.318-6.379-36.732 1.997-58.24 5.52-1.715 13.706-.428 24.554 3.853 10.85 4.283 18.794 7.952 23.84 10.994 5.046 3.041 9.089 5.618 12.135 7.708 17.705-4.947 35.976-7.421 54.818-7.421s37.117 2.474 54.823 7.421l10.849-6.849c7.419-4.57 16.18-8.758 26.262-12.565 10.088-3.805 17.802-4.853 23.134-3.138 8.562 21.509 9.325 40.922 2.279 58.24 15.036 16.18 22.559 35.787 22.559 58.817 0 16.178-1.958 30.497-5.853 42.966-3.9 12.471-8.941 22.457-15.125 29.979-6.191 7.521-13.901 13.85-23.131 18.986-9.232 5.14-18.182 8.85-26.84 11.136-8.662 2.286-18.415 4.004-29.263 5.146 9.894 8.562 14.842 22.077 14.842 40.539v60.237c0 3.422 1.19 6.279 3.572 8.562 2.379 2.279 6.136 2.95 11.276 1.995 44.163-14.653 80.185-41.062 108.068-79.226 27.88-38.161 41.825-81.126 41.825-128.906-.01-39.771-9.818-76.454-29.414-110.049z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, undefined),\n    radix: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 25 25\",\n            fill: \"none\",\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 25C7.58173 25 4 21.4183 4 17C4 12.5817 7.58173 9 12 9V25Z\",\n                    fill: \"currentcolor\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 0H4V8H12V0Z\",\n                    fill: \"currentcolor\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M17 8C19.2091 8 21 6.20914 21 4C21 1.79086 19.2091 0 17 0C14.7909 0 13 1.79086 13 4C13 6.20914 14.7909 8 17 8Z\",\n                    fill: \"currentcolor\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, undefined),\n    aria: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            role: \"img\",\n            viewBox: \"0 0 24 24\",\n            fill: \"currentColor\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M13.966 22.624l-1.69-4.281H8.122l3.892-9.144 5.662 13.425zM8.884 1.376H0v21.248zm15.116 0h-8.884L24 22.624Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined),\n    npm: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1.763 0C.786 0 0 .786 0 1.763v20.474C0 23.214.786 24 1.763 24h20.474c.977 0 1.763-.786 1.763-1.763V1.763C24 .786 23.214 0 22.237 0zM5.13 5.323l13.837.019-.009 13.836h-3.464l.01-10.382h-3.456L12.04 19.17H5.113z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 69,\n            columnNumber: 5\n        }, undefined),\n    yarn: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 0C5.375 0 0 5.375 0 12s5.375 12 12 12 12-5.375 12-12S18.625 0 12 0zm.768 4.105c.183 0 .363.053.525.157.125.083.287.185.755 1.154.31-.088.468-.042.551-.019.204.056.366.19.463.375.477.917.542 2.553.334 3.605-.241 1.232-.755 2.029-1.131 2.576.324.329.778.899 1.117 1.825.278.774.31 1.478.273 2.015a5.51 5.51 0 0 0 .602-.329c.593-.366 1.487-.917 2.553-.931.714-.009 1.269.445 1.353 1.103a1.23 1.23 0 0 1-.945 1.362c-.649.158-.95.278-1.821.843-1.232.797-2.539 1.242-3.012 1.39a1.686 1.686 0 0 1-.704.343c-.737.181-3.266.315-3.466.315h-.046c-.783 0-1.214-.241-1.45-.491-.658.329-1.51.19-2.122-.134a1.078 1.078 0 0 1-.58-1.153 1.243 1.243 0 0 1-.153-.195c-.162-.25-.528-.936-.454-1.946.056-.723.556-1.367.88-1.71a5.522 5.522 0 0 1 .408-2.256c.306-.727.885-1.348 1.32-1.737-.32-.537-.644-1.367-.329-2.21.227-.602.412-.936.82-1.08h-.005c.199-.074.389-.153.486-.259a3.418 3.418 0 0 1 2.298-1.103c.037-.093.079-.185.125-.283.31-.658.639-1.029 1.024-1.168a.94.94 0 0 1 .328-.06zm.006.7c-.507.016-1.001 1.519-1.001 1.519s-1.27-.204-2.266.871c-.199.218-.468.334-.746.44-.079.028-.176.023-.417.672-.371.991.625 2.094.625 2.094s-1.186.839-1.626 1.881c-.486 1.144-.338 2.261-.338 2.261s-.843.732-.899 1.487c-.051.663.139 1.2.343 1.515.227.343.51.176.51.176s-.561.653-.037.931c.477.25 1.283.394 1.71-.037.31-.31.371-1.001.486-1.283.028-.065.12.111.209.199.097.093.264.195.264.195s-.755.324-.445 1.066c.102.246.468.403 1.066.398.222-.005 2.664-.139 3.313-.296.375-.088.505-.283.505-.283s1.566-.431 2.998-1.357c.917-.598 1.293-.76 2.034-.936.612-.148.57-1.098-.241-1.084-.839.009-1.575.44-2.196.825-1.163.718-1.742.672-1.742.672l-.018-.032c-.079-.13.371-1.293-.134-2.678-.547-1.515-1.413-1.881-1.344-1.997.297-.5 1.038-1.297 1.334-2.78.176-.899.13-2.377-.269-3.151-.074-.144-.732.241-.732.241s-.616-1.371-.788-1.483a.271.271 0 0 0-.157-.046z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 77,\n            columnNumber: 5\n        }, undefined),\n    pnpm: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M0 0v7.5h7.5V0zm8.25 0v7.5h7.498V0zm8.25 0v7.5H24V0zM8.25 8.25v7.5h7.498v-7.5zm8.25 0v7.5H24v-7.5zM0 16.5V24h7.5v-7.5zm8.25 0V24h7.498v-7.5zm8.25 0V24H24v-7.5z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 85,\n            columnNumber: 5\n        }, undefined),\n    react: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 6.38.32.187.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.572-.895-1.096-1.345-1.565.44-.572.895-1.095 1.345-1.565zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.345.034.46 0 .915-.01 1.36-.034-.44.572-.895 1.095-1.345 1.565-.455-.47-.91-.993-1.36-1.565z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 93,\n            columnNumber: 5\n        }, undefined),\n    tailwind: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12.001,4.8c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 C13.666,10.618,15.027,12,18.001,12c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C16.337,6.182,14.976,4.8,12.001,4.8z M6.001,12c-3.2,0-5.2,1.6-6,4.8c1.2-1.6,2.6-2.2,4.2-1.8c0.913,0.228,1.565,0.89,2.288,1.624 c1.177,1.194,2.538,2.576,5.512,2.576c3.2,0,5.2-1.6,6-4.8c-1.2,1.6-2.6,2.2-4.2,1.8c-0.913-0.228-1.565-0.89-2.288-1.624 C10.337,13.382,8.976,12,6.001,12z M106.33,135.42H92.964c-3.779,0-5.886,2.493-5.886,6.282v45.317c0,4.04,2.416,6.282,5.663,6.282s5.663-2.242,5.663-6.282v-13.231h8.379c10.341,0,18.875-7.326,18.875-19.107C125.659,143.152,117.425,135.42,106.33,135.42z M106.108,163.158h-7.703v-17.097h7.703c4.755,0,7.78,3.711,7.78,8.553C113.878,159.447,110.863,163.158,106.108,163.158z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 101,\n            columnNumber: 5\n        }, undefined),\n    google: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            role: \"img\",\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                fill: \"currentColor\",\n                d: \"M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 109,\n            columnNumber: 5\n        }, undefined),\n    apple: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            role: \"img\",\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M13.966 22.624l-1.69-4.281H8.122l3.892-9.144 5.662 13.425zM8.884 1.376H0v21.248zm15.116 0h-8.884L24 22.624Z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 117,\n            columnNumber: 5\n        }, undefined),\n    paypal: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            role: \"img\",\n            viewBox: \"0 0 24 24\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.563.563 0 0 0-.556.479l-1.187 7.527h-.506l-.24 1.516a.56.56 0 0 0 .554.647h3.882c.46 0 .85-.334.922-.788.06-.26.76-4.852.816-5.09a.932.932 0 0 1 .923-.788h.58c3.76 0 6.705-1.528 7.565-5.946.36-1.847.174-3.388-.777-4.471z\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 125,\n            columnNumber: 5\n        }, undefined),\n    spinner: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M21 12a9 9 0 1 1-6.219-8.56\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 133,\n            columnNumber: 5\n        }, undefined),\n    pdf: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            height: \"24\",\n            width: \"24\",\n            version: \"1.1\",\n            id: \"Layer_1\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            xmlSpace: \"preserve\",\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        style: {\n                            fill: \"#E2574C\"\n                        },\n                        d: \"M38.658,0h164.23l87.049,86.711v203.227c0,10.679-8.659,19.329-19.329,19.329H38.658   c-10.67,0-19.329-8.65-19.329-19.329V19.329C19.329,8.65,27.989,0,38.658,0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        style: {\n                            fill: \"#B53629\"\n                        },\n                        d: \"M289.658,86.981h-67.372c-10.67,0-19.329-8.659-19.329-19.329V0.193L289.658,86.981z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        style: {\n                            fill: \"#FFFFFF\"\n                        },\n                        d: \"M217.434,146.544c3.238,0,4.823-2.822,4.823-5.557c0-2.832-1.653-5.567-4.823-5.567h-18.44   c-3.605,0-5.615,2.986-5.615,6.282v45.317c0,4.04,2.3,6.282,5.412,6.282c3.093,0,5.403-2.242,5.403-6.282v-12.438h11.153   c3.46,0,5.19-2.832,5.19-5.644c0-2.754-1.73-5.49-5.19-5.49h-11.153v-16.903C204.194,146.544,217.434,146.544,217.434,146.544z   M155.107,135.42h-13.492c-3.663,0-6.263,2.513-6.263,6.243v45.395c0,4.629,3.74,6.079,6.417,6.079h14.159   c16.758,0,27.824-11.027,27.824-28.047C183.743,147.095,173.325,135.42,155.107,135.42z M155.755,181.946h-8.225v-35.334h7.413   c11.221,0,16.101,7.529,16.101,17.918C171.044,174.253,166.25,181.946,155.755,181.946z M106.33,135.42H92.964   c-3.779,0-5.886,2.493-5.886,6.282v45.317c0,4.04,2.416,6.282,5.663,6.282s5.663-2.242,5.663-6.282v-13.231h8.379   c10.341,0,18.875-7.326,18.875-19.107C125.659,143.152,117.425,135.42,106.33,135.42z M106.108,163.158h-7.703v-17.097h7.703   c4.755,0,7.78,3.711,7.78,8.553C113.878,159.447,110.863,163.158,106.108,163.158z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 149,\n            columnNumber: 5\n        }, undefined)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/icons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUErQjtBQUVFO0FBS2pDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCw4VkFDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9jOTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiO1xuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtOSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLXRyYW5zcGFyZW50IHB4LTMgcHktMSB0ZXh0LXNtIHNoYWRvdy1zbSB0cmFuc2l0aW9uLWNvbG9ycyBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gZmlsZTp0ZXh0LWZvcmVncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0xIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgICk7XG4gIH1cbik7XG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIjtcblxuZXhwb3J0IHsgSW5wdXQgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRStCO0FBQ3lCO0FBQ1U7QUFFakM7QUFFakMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRWxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiO1xuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIjtcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcblxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxuKTtcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpO1xuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lO1xuXG5leHBvcnQgeyBMYWJlbCB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjdmEiLCJjbiIsImxhYmVsVmFyaWFudHMiLCJMYWJlbCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/password-input.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/password-input.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PasswordInput: () => (/* binding */ PasswordInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeOffIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeOffIcon!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeOffIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeOffIcon!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* __next_internal_client_entry_do_not_use__ PasswordInput auto */ \n\n\n\n\n\nconst PasswordInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    const [showPassword, setShowPassword] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const disabled = props.value === \"\" || props.value === undefined || props.disabled;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                type: showPassword ? \"text\" : \"password\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"hide-password-toggle pr-10\", className),\n                ref: ref,\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                type: \"button\",\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                onClick: ()=>setShowPassword((prev)=>!prev),\n                disabled: disabled,\n                children: [\n                    showPassword && !disabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeOffIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeOffIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"sr-only\",\n                        children: showPassword ? \"Hide password\" : \"Show password\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: `\n\t\t\t\t\t.hide-password-toggle::-ms-reveal,\n\t\t\t\t\t.hide-password-toggle::-ms-clear {\n\t\t\t\t\t\tvisibility: hidden;\n\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\tdisplay: none;\n\t\t\t\t\t}\n\t\t\t\t`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\components\\\\ui\\\\password-input.tsx\",\n        lineNumber: 17,\n        columnNumber: 7\n    }, undefined);\n});\nPasswordInput.displayName = \"PasswordInput\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/password-input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/../../node_modules/@supabase/ssr/dist/module/index.js\");\n\nfunction createSupabaseClient() {\n    if (false) {}\n    if (false) {}\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://zkxqdklfxrlgnivamfaa.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpreHFka2xmeHJsZ25pdmFtZmFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4NTc5NTMsImV4cCI6MjA2MjQzMzk1M30.uk5huCuz1ywzZmD2dsCg2AWBRh8TvsHd1X7R8TozALE\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRDtBQUU3QyxTQUFTQztJQUNkLElBQUksS0FBcUMsRUFBRSxFQUUxQztJQUNELElBQUksS0FBMEMsRUFBRSxFQUUvQztJQUVELE9BQU9ELGtFQUFtQkEsQ0FDeEJFLDBDQUFvQyxFQUNwQ0Esa05BQXlDO0FBRTdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL2xpYi9zdXBhYmFzZS9jbGllbnQudHM/MGY5NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSBcIkBzdXBhYmFzZS9zc3JcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVN1cGFiYXNlQ2xpZW50KCkge1xuICBpZiAoIXByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBpcyBub3QgZGVmaW5lZFwiKTtcbiAgfVxuICBpZiAoIXByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgaXMgbm90IGRlZmluZWRcIik7XG4gIH1cblxuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwsXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVlcbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlU3VwYWJhc2VDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiRXJyb3IiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUNKO0FBRWxDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e9a5eb6263ca\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9kM2QwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTlhNWViNjI2M2NhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\NagaRaju\deepaudit-canvas\apps\web\src\app\auth\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var nuqs_adapters_next_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! nuqs/adapters/next/app */ \"(rsc)/../../node_modules/nuqs/dist/adapters/next/app.js\");\n\n\n\n\n\nconst metadata = {\n    title: \"DeepAudit Canvas\",\n    description: \"AI-powered audit work paper creation and accounting assistance\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"min-h-full\", (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(nuqs_adapters_next_app__WEBPACK_IMPORTED_MODULE_4__.NuqsAdapter, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NagaRaju\\\\deepaudit-canvas\\\\apps\\\\web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUxpQjtBQUVVO0FBQ29CO0FBTTlDLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVdULDhDQUFFQSxDQUFDLGNBQWNELCtKQUFlO3NCQUMvQyw0RUFBQ0UsK0RBQVdBOzBCQUFFSzs7Ozs7Ozs7Ozs7Ozs7OztBQUl0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcbmltcG9ydCB7IE51cXNBZGFwdGVyIH0gZnJvbSBcIm51cXMvYWRhcHRlcnMvbmV4dC9hcHBcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7XG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkRlZXBBdWRpdCBDYW52YXNcIixcbiAgZGVzY3JpcHRpb246IFwiQUktcG93ZXJlZCBhdWRpdCB3b3JrIHBhcGVyIGNyZWF0aW9uIGFuZCBhY2NvdW50aW5nIGFzc2lzdGFuY2VcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPVwiaC1zY3JlZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17Y24oXCJtaW4taC1mdWxsXCIsIGludGVyLmNsYXNzTmFtZSl9PlxuICAgICAgICA8TnVxc0FkYXB0ZXI+e2NoaWxkcmVufTwvTnVxc0FkYXB0ZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiY24iLCJOdXFzQWRhcHRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(action-browser)/./src/components/auth/login/actions.ts":
/*!**********************************************!*\
  !*** ./src/components/auth/login/actions.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   login: () => (/* binding */ login)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/../../node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_cache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/cache */ \"(action-browser)/../../node_modules/next/cache.js\");\n/* harmony import */ var next_cache__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_cache__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(action-browser)/../../node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/server */ \"(action-browser)/./src/lib/supabase/server.ts\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"1fbbb49c13fa01403a9b894495b52e9b9d4f23fa\":\"login\"} */ \n\n\n\n\nasync function login(input) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_4__.createClient)();\n    const data = {\n        email: input.email,\n        password: input.password\n    };\n    const { error } = await supabase.auth.signInWithPassword(data);\n    if (error) {\n        console.error(error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/auth/login?error=true\");\n    }\n    (0,next_cache__WEBPACK_IMPORTED_MODULE_2__.revalidatePath)(\"/\", \"layout\");\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(\"/\");\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_5__.ensureServerEntryExports)([\n    login\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(\"1fbbb49c13fa01403a9b894495b52e9b9d4f23fa\", login);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvbG9naW4vYWN0aW9ucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNEO0FBRVU7QUFHOUMsZUFBZUcsTUFBTUMsS0FBMEI7SUFDcEQsTUFBTUMsV0FBV0gsa0VBQVlBO0lBRTdCLE1BQU1JLE9BQU87UUFDWEMsT0FBT0gsTUFBTUcsS0FBSztRQUNsQkMsVUFBVUosTUFBTUksUUFBUTtJQUMxQjtJQUVBLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTUosU0FBU0ssSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ0w7SUFFekQsSUFBSUcsT0FBTztRQUNURyxRQUFRSCxLQUFLLENBQUNBO1FBQ2RSLHlEQUFRQSxDQUFDO0lBQ1g7SUFFQUQsMERBQWNBLENBQUMsS0FBSztJQUNwQkMseURBQVFBLENBQUM7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9jb21wb25lbnRzL2F1dGgvbG9naW4vYWN0aW9ucy50cz9jN2Y3Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHNlcnZlclwiO1xuXG5pbXBvcnQgeyByZXZhbGlkYXRlUGF0aCB9IGZyb20gXCJuZXh0L2NhY2hlXCI7XG5pbXBvcnQgeyByZWRpcmVjdCB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcblxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIkAvbGliL3N1cGFiYXNlL3NlcnZlclwiO1xuaW1wb3J0IHsgTG9naW5XaXRoRW1haWxJbnB1dCB9IGZyb20gXCIuL0xvZ2luXCI7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2dpbihpbnB1dDogTG9naW5XaXRoRW1haWxJbnB1dCkge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuXG4gIGNvbnN0IGRhdGEgPSB7XG4gICAgZW1haWw6IGlucHV0LmVtYWlsLFxuICAgIHBhc3N3b3JkOiBpbnB1dC5wYXNzd29yZCxcbiAgfTtcblxuICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhQYXNzd29yZChkYXRhKTtcblxuICBpZiAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICByZWRpcmVjdChcIi9hdXRoL2xvZ2luP2Vycm9yPXRydWVcIik7XG4gIH1cblxuICByZXZhbGlkYXRlUGF0aChcIi9cIiwgXCJsYXlvdXRcIik7XG4gIHJlZGlyZWN0KFwiL1wiKTtcbn1cbiJdLCJuYW1lcyI6WyJyZXZhbGlkYXRlUGF0aCIsInJlZGlyZWN0IiwiY3JlYXRlQ2xpZW50IiwibG9naW4iLCJpbnB1dCIsInN1cGFiYXNlIiwiZGF0YSIsImVtYWlsIiwicGFzc3dvcmQiLCJlcnJvciIsImF1dGgiLCJzaWduSW5XaXRoUGFzc3dvcmQiLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./src/components/auth/login/actions.ts\n");

/***/ }),

/***/ "(action-browser)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(action-browser)/../../node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(action-browser)/../../node_modules/next/dist/api/headers.js\");\n\n\nfunction createClient() {\n    if (false) {}\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zkxqdklfxrlgnivamfaa.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpreHFka2xmeHJsZ25pdmFtZmFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4NTc5NTMsImV4cCI6MjA2MjQzMzk1M30.uk5huCuz1ywzZmD2dsCg2AWBRh8TvsHd1X7R8TozALE\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUNKO0FBRWxDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0BvcGVuY2FudmFzL3dlYi8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/../../node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxFQUFpRjs7QUFFakYsRUFBRSxpRUFBZTtBQUNqQix1QkFBdUI7QUFDdkIscUJBQXFCLDhGQUFtQjs7QUFFeEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG9wZW5jYW52YXMvd2ViLy4vc3JjL2FwcC9mYXZpY29uLmljbz80ZGU4Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/nuqs","vendor-chunks/@radix-ui","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravis%5CDocuments%5CProjects%5CNagaRaju%5Cdeepaudit-canvas%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();