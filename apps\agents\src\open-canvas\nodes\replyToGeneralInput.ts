import { LangGraphRunnableConfig } from "@langchain/langgraph";
import { getArtifactContent } from "@opencanvas/shared/utils/artifacts";
import {
  createContextDocumentMessages,
  formatArtifactContentWithTemplate,
  getModelFromConfig,
  isUsingO1MiniModel,
} from "../../utils.js";
import { CURRENT_ARTIFACT_PROMPT, NO_ARTIFACT_PROMPT } from "../prompts.js";
import {
  OpenCanvasGraphAnnotation,
  OpenCanvasGraphReturnType,
} from "../state.js";

/**
 * Generate responses to questions. Does not generate artifacts.
 */
export const replyToGeneralInput = async (
  state: typeof OpenCanvasGraphAnnotation.State,
  config: LangGraphRunnableConfig
): Promise<OpenCanvasGraphReturnType> => {
  const smallModel = await getModelFromConfig(config);

  const prompt = `You are an AI assistant specialized in audit and accounting, tasked with responding to the user's question.

You should provide professional, accurate, and helpful responses to audit and accounting questions. When appropriate, reference relevant accounting standards (IFRS, GAAP, ISA, etc.) and use professional terminology.

The user may have generated audit work papers or accounting documents in the past. Use the following artifacts as context when responding to the user's question.

{currentArtifactPrompt}

When answering questions:
- Maintain a professional tone appropriate for audit and accounting contexts
- Provide clear, concise explanations of complex accounting and auditing concepts
- Reference authoritative sources when discussing accounting standards or audit methodologies
- If discussing financial data, ensure explanations are precise and technically accurate
- If the user asks about audit procedures or methodologies, provide practical guidance based on professional standards
- When discussing regulatory requirements, specify which jurisdiction's rules you're referring to (US GAAP, IFRS, etc.)
- If you're uncertain about specific local regulations, acknowledge this limitation in your response`;

  const currentArtifactContent = state.artifact
    ? getArtifactContent(state.artifact)
    : undefined;

  const formattedPrompt = prompt
    .replace(
      "{currentArtifactPrompt}",
      currentArtifactContent
        ? formatArtifactContentWithTemplate(
            CURRENT_ARTIFACT_PROMPT,
            currentArtifactContent
          )
        : NO_ARTIFACT_PROMPT
    );

  const contextDocumentMessages = await createContextDocumentMessages(config);
  const isO1MiniModel = isUsingO1MiniModel(config);
  const response = await smallModel.invoke([
    { role: isO1MiniModel ? "user" : "system", content: formattedPrompt },
    ...contextDocumentMessages,
    ...state._messages,
  ]);

  return {
    messages: [response],
    _messages: [response],
  };
};
