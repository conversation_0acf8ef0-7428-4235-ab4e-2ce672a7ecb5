import { createClient } from "@/lib/supabase/server";
import { TrialBalanceEntry, JournalLedgerEntry } from "@/types";

/**
 * Upload trial balance entries to the database
 * @param entries Array of trial balance entries to upload
 * @param engagementId The engagement ID to associate with the entries
 * @param userId The user ID of the person uploading the data
 * @returns Result of the upload operation
 */
export async function uploadTrialBalance(
  entries: TrialBalanceEntry[],
  engagementId: string,
  userId: string
) {
  const supabase = createClient();
  
  // Add created_by and timestamps to each entry
  const entriesWithMetadata = entries.map((entry) => ({
    ...entry,
    engagement_id: engagementId,
    created_by: userId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));

  const { data, error } = await supabase
    .from("trial_balance")
    .insert(entriesWithMetadata)
    .select();

  if (error) {
    console.error("Error uploading trial balance:", error);
    throw new Error(`Failed to upload trial balance: ${error.message}`);
  }

  return { success: true, data };
}

/**
 * Upload journal ledger entries to the database
 * @param entries Array of journal ledger entries to upload
 * @param engagementId The engagement ID to associate with the entries
 * @param userId The user ID of the person uploading the data
 * @returns Result of the upload operation
 */
export async function uploadJournalLedger(
  entries: JournalLedgerEntry[],
  engagementId: string,
  userId: string
) {
  const supabase = createClient();
  
  // Add created_by and timestamps to each entry
  const entriesWithMetadata = entries.map((entry) => ({
    ...entry,
    engagement_id: engagementId,
    created_by: userId,
    created_at: new Date().toISOString(),
  }));

  const { data, error } = await supabase
    .from("journal_ledger")
    .insert(entriesWithMetadata)
    .select();

  if (error) {
    console.error("Error uploading journal ledger:", error);
    throw new Error(`Failed to upload journal ledger: ${error.message}`);
  }

  return { success: true, data };
}

/**
 * Get all trial balance entries for an engagement
 * @param engagementId The engagement ID to get entries for
 * @returns Array of trial balance entries
 */
export async function getTrialBalanceForEngagement(engagementId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from("trial_balance")
    .select("*")
    .eq("engagement_id", engagementId)
    .order("account_code", { ascending: true });

  if (error) {
    console.error("Error fetching trial balance:", error);
    throw new Error(`Failed to fetch trial balance: ${error.message}`);
  }

  return data;
}

/**
 * Get all journal ledger entries for an engagement
 * @param engagementId The engagement ID to get entries for
 * @returns Array of journal ledger entries
 */
export async function getJournalLedgerForEngagement(engagementId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from("journal_ledger")
    .select("*")
    .eq("engagement_id", engagementId)
    .order("transaction_date", { ascending: false });

  if (error) {
    console.error("Error fetching journal ledger:", error);
    throw new Error(`Failed to fetch journal ledger: ${error.message}`);
  }

  return data;
}

/**
 * Delete all trial balance entries for an engagement
 * @param engagementId The engagement ID to delete entries for
 * @returns Result of the delete operation
 */
export async function deleteTrialBalanceForEngagement(engagementId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from("trial_balance")
    .delete()
    .eq("engagement_id", engagementId);

  if (error) {
    console.error("Error deleting trial balance:", error);
    throw new Error(`Failed to delete trial balance: ${error.message}`);
  }

  return { success: true, data };
}

/**
 * Delete all journal ledger entries for an engagement
 * @param engagementId The engagement ID to delete entries for
 * @returns Result of the delete operation
 */
export async function deleteJournalLedgerForEngagement(engagementId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from("journal_ledger")
    .delete()
    .eq("engagement_id", engagementId);

  if (error) {
    console.error("Error deleting journal ledger:", error);
    throw new Error(`Failed to delete journal ledger: ${error.message}`);
  }

  return { success: true, data };
}
