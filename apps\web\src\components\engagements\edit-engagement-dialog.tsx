"use client";

import { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Edit2 } from "lucide-react";

interface Engagement {
  id: string;
  name: string;
  client_name: string;
  description: string | null;
  start_date: string | null;
  end_date: string | null;
  status: string;
  created_by: string;
}

interface EditEngagementDialogProps {
  engagement: Engagement;
  onEngagementUpdated: () => void;
  trigger?: React.ReactNode;
}

export function EditEngagementDialog({
  engagement,
  onEngagementUpdated,
  trigger,
}: EditEngagementDialogProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Form state
  const [name, setName] = useState(engagement.name);
  const [clientName, setClientName] = useState(engagement.client_name);
  const [description, setDescription] = useState(engagement.description || "");
  const [startDate, setStartDate] = useState(engagement.start_date || "");
  const [endDate, setEndDate] = useState(engagement.end_date || "");
  const [status, setStatus] = useState(engagement.status || "Active");

  // Form validation
  const [errors, setErrors] = useState<{
    name?: string;
    clientName?: string;
    startDate?: string;
    endDate?: string;
  }>({});

  // Update form when engagement changes
  useEffect(() => {
    if (engagement) {
      setName(engagement.name);
      setClientName(engagement.client_name);
      setDescription(engagement.description || "");
      setStartDate(engagement.start_date || "");
      setEndDate(engagement.end_date || "");
      setStatus(engagement.status || "Active");
    }
  }, [engagement]);

  const validateForm = () => {
    const newErrors: {
      name?: string;
      clientName?: string;
      startDate?: string;
      endDate?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = "Engagement name is required";
    }

    if (!clientName.trim()) {
      newErrors.clientName = "Client name is required";
    }

    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      newErrors.endDate = "End date must be after start date";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/engagements/${engagement.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          client_name: clientName,
          description,
          start_date: startDate || null,
          end_date: endDate || null,
          status,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update engagement");
      }

      toast({
        title: "Engagement updated",
        description: `${name} has been updated successfully.`,
      });

      // Close dialog
      setOpen(false);
      
      // Refresh engagements list
      onEngagementUpdated();
    } catch (error) {
      console.error("Error updating engagement:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update engagement",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const defaultTrigger = (
    <Button variant="ghost" size="icon" className="h-8 w-8">
      <Edit2 className="h-4 w-4" />
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger ? (
        <div onClick={(e) => e.stopPropagation()}>{trigger}</div>
      ) : (
        <div onClick={(e) => e.stopPropagation()}>{defaultTrigger}</div>
      )}
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Engagement</DialogTitle>
            <DialogDescription>
              Update the engagement details below.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name*
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                required
              />
              {errors.name && (
                <p className="col-span-3 col-start-2 text-sm text-red-500">
                  {errors.name}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="client-name" className="text-right">
                Client*
              </Label>
              <Input
                id="client-name"
                value={clientName}
                onChange={(e) => setClientName(e.target.value)}
                className="col-span-3"
                required
              />
              {errors.clientName && (
                <p className="col-span-3 col-start-2 text-sm text-red-500">
                  {errors.clientName}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="start-date" className="text-right">
                Start Date
              </Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="end-date" className="text-right">
                End Date
              </Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="col-span-3"
              />
              {errors.endDate && (
                <p className="col-span-3 col-start-2 text-sm text-red-500">
                  {errors.endDate}
                </p>
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select
                value={status}
                onValueChange={(value) => setStatus(value)}
              >
                <SelectTrigger id="status" className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Planning">Planning</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Review">Review</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
