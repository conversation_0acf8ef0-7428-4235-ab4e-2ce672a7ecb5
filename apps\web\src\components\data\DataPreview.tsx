"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ColumnMapping } from "@/types";
import { cn } from "@/lib/utils";

interface DataPreviewProps {
  data: Record<string, any>[];
  mappings: ColumnMapping[];
  className?: string;
  maxRows?: number;
}

export function DataPreview({
  data,
  mappings,
  className,
  maxRows = 5,
}: DataPreviewProps) {
  if (!data || data.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="text-lg">Data Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">No data to preview</p>
        </CardContent>
      </Card>
    );
  }

  // Get the columns to display based on mappings
  const columnsToDisplay = mappings
    .filter((mapping) => mapping.sourceColumn)
    .map((mapping) => ({
      source: mapping.sourceColumn,
      target: mapping.targetColumn,
    }));

  // Limit the number of rows to display
  const rowsToDisplay = data.slice(0, maxRows);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="text-lg">Data Preview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                {columnsToDisplay.map((column) => (
                  <TableHead key={column.source}>
                    <div className="flex flex-col">
                      <span className="font-medium">{column.target}</span>
                      <span className="text-xs text-gray-500">
                        {column.source}
                      </span>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {rowsToDisplay.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columnsToDisplay.map((column) => (
                    <TableCell key={`${rowIndex}-${column.source}`}>
                      {row[column.source] !== undefined
                        ? String(row[column.source])
                        : ""}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {data.length > maxRows && (
          <p className="text-xs text-gray-500 mt-2">
            Showing {maxRows} of {data.length} rows
          </p>
        )}
      </CardContent>
    </Card>
  );
}
