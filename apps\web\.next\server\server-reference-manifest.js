self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"1fbbb49c13fa01403a9b894495b52e9b9d4f23fa\": {\n      \"workers\": {\n        \"app/auth/login/page\": \"(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5Clogin%5C%5Cactions.ts%22%2C%5B%22login%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/auth/login/page\": \"action-browser\"\n      }\n    },\n    \"ad513d525b111338b9f2eee3ee1df5b312f1fad1\": {\n      \"workers\": {\n        \"app/auth/signup/page\": \"(action-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CNagaRaju%5C%5Cdeepaudit-canvas%5C%5Capps%5C%5Cweb%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5Csignup%5C%5Cactions.ts%22%2C%5B%22signup%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/auth/signup/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"