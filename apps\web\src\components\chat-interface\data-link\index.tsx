import NextImage from "next/image";
import { useRouter } from "next/navigation";
import DatabaseIcon from "@/components/icons/svg/DatabaseIcon.svg";
import { useSearchParams } from "next/navigation";

export default function DataLink() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const handleClick = () => {
    // Get the current engagement ID from the URL
    const engagementId = searchParams.get("engagementId");
    
    // Navigate to the data page with the engagement ID
    if (engagementId) {
      router.push(`/data?engagementId=${engagementId}`);
    } else {
      router.push("/data");
    }
  };

  return (
    <div 
      onClick={handleClick}
      className="min-w-[120px] w-[120px] bg-transparent shadow-none focus:outline-none cursor-pointer hover:bg-gray-100 rounded transition-colors border-none text-gray-600 h-9 px-3 py-2 text-sm focus:ring-1 focus:ring-ring flex items-center"
    >
      <NextImage
        alt="Data icon"
        src={DatabaseIcon}
        width={14}
        height={14}
        className="mr-2"
      />
      <span className="flex flex-row items-center justify-start gap-2">
        Data
      </span>
    </div>
  );
}
