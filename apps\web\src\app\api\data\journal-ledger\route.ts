import { NextRequest, NextResponse } from "next/server";
import { verifyUserAuthenticated } from "@/lib/supabase/verify_user_server";
import { uploadJournalLedger } from "@/lib/services/data-service";
import { JournalLedgerEntry } from "@/types";
import { createClient } from "@/lib/supabase/server";

export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = authRes.user.id;
    const { entries, engagementId } = await req.json() as {
      entries: JournalLedgerEntry[];
      engagementId: string;
    };

    // Validate request
    if (!entries || !Array.isArray(entries) || entries.length === 0) {
      return NextResponse.json(
        { error: "No journal ledger entries provided" },
        { status: 400 }
      );
    }

    if (!engagementId) {
      return NextResponse.json(
        { error: "Engagement ID is required" },
        { status: 400 }
      );
    }

    // Validate required fields in each entry
    const invalidEntries = entries.filter(
      (entry) =>
        !entry.account_code ||
        !entry.account_name ||
        !entry.transaction_date ||
        entry.amount === undefined
    );

    if (invalidEntries.length > 0) {
      return NextResponse.json(
        {
          error: "Some entries are missing required fields",
          invalidEntries,
        },
        { status: 400 }
      );
    }

    // Upload journal ledger entries
    const result = await uploadJournalLedger(entries, engagementId, userId);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Error uploading journal ledger:", error);
    return NextResponse.json(
      { error: `Failed to upload journal ledger: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get engagement ID from query params
    const url = new URL(req.url);
    const engagementId = url.searchParams.get("engagementId");

    if (!engagementId) {
      return NextResponse.json(
        { error: "Engagement ID is required" },
        { status: 400 }
      );
    }

    // Get journal ledger entries for the engagement
    const supabase = createClient();
    const { data, error } = await supabase
      .from("journal_ledger")
      .select("*")
      .eq("engagement_id", engagementId)
      .order("transaction_date", { ascending: false });

    if (error) {
      console.error("Error fetching journal ledger:", error);
      return NextResponse.json(
        { error: `Failed to fetch journal ledger: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching journal ledger:", error);
    return NextResponse.json(
      { error: `Failed to fetch journal ledger: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Verify user is authenticated
    const authRes = await verifyUserAuthenticated();
    if (!authRes?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get engagement ID from query params
    const url = new URL(req.url);
    const engagementId = url.searchParams.get("engagementId");

    if (!engagementId) {
      return NextResponse.json(
        { error: "Engagement ID is required" },
        { status: 400 }
      );
    }

    // Delete journal ledger entries for the engagement
    const supabase = createClient();
    const { error } = await supabase
      .from("journal_ledger")
      .delete()
      .eq("engagement_id", engagementId);

    if (error) {
      console.error("Error deleting journal ledger:", error);
      return NextResponse.json(
        { error: `Failed to delete journal ledger: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, message: "Journal ledger deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting journal ledger:", error);
    return NextResponse.json(
      { error: `Failed to delete journal ledger: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}
