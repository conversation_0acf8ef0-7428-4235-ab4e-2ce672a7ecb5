# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
**/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.yarn/cache

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/dist
**/dist
.turbo/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files


# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

credentials.json

# LangGraph API
.langgraph_api
