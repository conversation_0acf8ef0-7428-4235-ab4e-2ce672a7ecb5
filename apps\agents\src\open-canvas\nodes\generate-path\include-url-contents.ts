import { HumanMessage } from "@langchain/core/messages";
import { traceable } from "langsmith/traceable";

/**
 * This functionality has been disabled as part of removing web search features.
 * The function now always returns undefined, which means URLs in messages will not
 * be processed or have their contents fetched.
 */
async function includeURLContentsFunc(
  _message: HumanMessage,
  _urls: string[]
): Promise<HumanMessage | undefined> {
  // Web search functionality has been removed
  return undefined;
}

export const includeURLContents = traceable(includeURLContentsFunc, {
  name: "include_url_contents",
});
