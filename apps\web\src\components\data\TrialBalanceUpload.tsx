"use client";

import { useState, useEffect } from "react";
import { FileUpload } from "./FileUpload";
import { ColumnMapper } from "./ColumnMapper";
import { DataPreview } from "./DataPreview";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { Loader2, AlertCircle, CheckCircle2, Calendar } from "lucide-react";
import { parseFile, getColumnHeaders, mapData } from "@/lib/utils/file-parser";
import { ColumnMapping, TrialBalanceEntry } from "@/types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface TrialBalanceUploadProps {
  engagementId: string;
  onUploadComplete?: () => void;
}

export function TrialBalanceUpload({
  engagementId,
  onUploadComplete,
}: TrialBalanceUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<Record<string, any>[]>([]);
  const [sourceColumns, setSourceColumns] = useState<string[]>([]);
  const [mappings, setMappings] = useState<ColumnMapping[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [balanceDate, setBalanceDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const { toast } = useToast();

  // Define target columns for trial balance
  const targetColumns = [
    { name: "account_code", required: true },
    { name: "account_name", required: true },
    { name: "amount", required: true },
    { name: "account_type", required: false },
    { name: "financial_statement_category", required: false },
    { name: "audit_testing_section", required: false },
    { name: "balance_date", required: false }, // Optional since we're using the date selector
  ];

  // Parse file when selected
  useEffect(() => {
    const parseSelectedFile = async () => {
      if (!file) {
        setParsedData([]);
        setSourceColumns([]);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        setSuccess(false);

        const data = await parseFile(file);
        setParsedData(data);

        const headers = getColumnHeaders(data);
        setSourceColumns(headers);
      } catch (err) {
        setError(`Failed to parse file: ${(err as Error).message}`);
        toast({
          title: "Error parsing file",
          description: (err as Error).message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    parseSelectedFile();
  }, [file, toast]);

  const handleFileSelect = (selectedFile: File) => {
    setFile(selectedFile);
    setSuccess(false);
    setError(null);
  };

  const handleMappingsChange = (newMappings: ColumnMapping[]) => {
    console.log("New mappings received:", newMappings);

    // Only update if we have valid mappings
    if (newMappings && newMappings.length > 0) {
      setMappings(newMappings);
    }
  };

  const handleUpload = async () => {
    if (!file || !parsedData.length || !mappings.length) {
      setError("Please select a file and map the columns");
      return;
    }

    if (!balanceDate) {
      setError("Please select a balance date");
      return;
    }

    // Validate balance date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(balanceDate)) {
      setError("Invalid balance date format. Please use YYYY-MM-DD format.");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setSuccess(false);

      // Map the data using the column mappings
      const mappedData = mapData<TrialBalanceEntry>(parsedData, mappings);

      // Validate that we have the required mappings
      const requiredColumns = ["account_code", "account_name", "amount"];

      // Log current mappings for debugging
      console.log("Current mappings before validation:", JSON.stringify(mappings));

      const missingMappings = requiredColumns.filter(col => {
        const mapping = mappings.find(m => m.targetColumn === col);
        return !mapping || !mapping.sourceColumn || mapping.sourceColumn === "none";
      });

      if (missingMappings.length > 0) {
        throw new Error(`Missing required column mappings: ${missingMappings.join(", ")}. Please map all required columns.`);
      }

      // Check if we have any data to upload
      if (mappedData.length === 0) {
        throw new Error("No data to upload. Please ensure your file contains valid data.");
      }

      // Apply the selected balance date to all entries and ensure all required fields are present
      const entriesWithBalanceDate = mappedData.map((entry, index) => {
        // Ensure all required fields have values
        if (!entry.account_code || !entry.account_name || entry.amount === undefined) {
          throw new Error(`Row ${index + 1} is missing required fields. Please check your column mappings.`);
        }

        // Ensure amount is a valid number
        let amount: number;
        try {
          amount = typeof entry.amount === 'number'
            ? entry.amount
            : parseFloat(String(entry.amount).replace(/[^\d.-]/g, ''));

          if (isNaN(amount)) {
            throw new Error(`Invalid amount value in row ${index + 1}`);
          }
        } catch (e) {
          throw new Error(`Invalid amount value in row ${index + 1}. Please ensure all amount values are valid numbers.`);
        }

        return {
          ...entry,
          balance_date: balanceDate,
          amount: amount
        };
      });

      // Upload the data
      const response = await fetch("/api/data/trial-balance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          entries: entriesWithBalanceDate,
          engagementId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Trial balance upload error:", errorData);

        // Handle different error cases
        if (errorData.invalidEntries && errorData.invalidEntries.length > 0) {
          throw new Error(`${errorData.error || "Some entries are missing required fields"}. Please check your data and try again.`);
        } else {
          throw new Error(errorData.error || "Failed to upload trial balance. Please try again.");
        }
      }

      setSuccess(true);
      toast({
        title: "Upload successful",
        description: `Successfully uploaded ${entriesWithBalanceDate.length} trial balance entries for ${balanceDate}`,
      });

      // Reset the form but keep the balance date
      setFile(null);
      setParsedData([]);
      setSourceColumns([]);
      setMappings([]);

      // Notify parent component
      if (onUploadComplete) {
        onUploadComplete();
      }
    } catch (err) {
      console.error("Trial balance upload error:", err);
      const errorMessage = (err as Error).message || "An unknown error occurred";
      setError(`Upload failed: ${errorMessage}`);
      toast({
        title: "Upload failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Upload Trial Balance</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="balance-date" className="text-right">
              Balance Date
            </Label>
            <div className="col-span-3 flex items-center">
              <Input
                id="balance-date"
                type="date"
                value={balanceDate}
                onChange={(e) => setBalanceDate(e.target.value)}
                className="w-full"
              />
              <Calendar className="ml-2 h-4 w-4 text-gray-500" />
            </div>
          </div>
          <FileUpload
            onFileSelect={handleFileSelect}
            acceptedFileTypes=".csv,.xlsx,.xls"
            maxSizeInMB={10}
          />
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Processing...</span>
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Success</AlertTitle>
          <AlertDescription className="text-green-700">
            Trial balance data for {balanceDate} uploaded successfully
          </AlertDescription>
        </Alert>
      )}

      {sourceColumns.length > 0 && !isLoading && !success && (
        <div className="space-y-6">
          <ColumnMapper
            sourceColumns={sourceColumns}
            targetColumns={targetColumns}
            onChange={handleMappingsChange}
          />

          {mappings.length > 0 && (
            <>
              <DataPreview
                data={parsedData}
                mappings={mappings}
                maxRows={5}
              />

              <div className="flex flex-col gap-4">
                {/* Check if required mappings are missing */}
                {(() => {
                  const requiredColumns = ["account_code", "account_name", "amount"];
                  const missingMappings = requiredColumns.filter(col => {
                    const mapping = mappings.find(m => m.targetColumn === col);
                    return !mapping || !mapping.sourceColumn || mapping.sourceColumn === "none";
                  });

                  if (missingMappings.length > 0) {
                    return (
                      <Alert variant="warning" className="bg-yellow-50 border-yellow-200">
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                        <AlertTitle className="text-yellow-800">Missing Required Mappings</AlertTitle>
                        <AlertDescription className="text-yellow-700">
                          Please map the following required columns: {missingMappings.join(", ")}
                        </AlertDescription>
                      </Alert>
                    );
                  }
                  return null;
                })()}

                <div className="flex justify-end">
                  <Button
                    onClick={handleUpload}
                    disabled={(() => {
                      // Check if any required mappings are missing
                      const requiredColumns = ["account_code", "account_name", "amount"];
                      const missingMappings = requiredColumns.filter(col => {
                        const mapping = mappings.find(m => m.targetColumn === col);
                        return !mapping || !mapping.sourceColumn || mapping.sourceColumn === "none";
                      });

                      return isLoading || mappings.length === 0 || missingMappings.length > 0;
                    })()}
                  >
                    {isLoading && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Upload Trial Balance
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}
